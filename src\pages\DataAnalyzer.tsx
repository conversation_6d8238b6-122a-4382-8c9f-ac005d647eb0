import { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Progress } from '../components/ui/progress';
import { useToast } from '../components/ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../components/ui/dialog';
import SchemaReviewPanel from '../components/SchemaReviewPanel';
import CombinationReviewPanel from '../components/CombinationReviewPanel';
import MultiFileSheetSelector from '../components/MultiFileSheetSelector';
import {
  FileSpreadsheet,
  Upload,
  AlertCircle,
  ArrowRight,
  X,
  Brain,
  Database,
  LineChart,
  Download,
  FileText,
  Tag
} from 'lucide-react';
import fileService, { DataSource, CombinedDataset } from '../services/excelService';

interface FileItem {
  file: File;
  name: string;
  type: string;
  size: number;
  selectedSheet?: string;
}

const DataAnalyzer = () => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [dataSources, setDataSources] = useState<DataSource[]>([]);

  // Combined datasets state
  const [combinedDatasets, setCombinedDatasets] = useState<Record<string, CombinedDataset>>({});
  const [showCombineModal, setShowCombineModal] = useState<boolean>(false);
  const [showSchemaReviewModal, setShowSchemaReviewModal] = useState<boolean>(false);
  const [selectedSourceIds, setSelectedSourceIds] = useState<string[]>([]);
  const [selectedSources, setSelectedSources] = useState<Array<{id: string, name: string}>>([]);
  const [combinedDatasetName, setCombinedDatasetName] = useState<string>('');
  const [joinStrategy, setJoinStrategy] = useState<'smart' | 'concat' | 'inner_join' | 'outer_join'>('smart');
  const [isCombining, setIsCombining] = useState<boolean>(false);
  const [fieldMappings, setFieldMappings] = useState<any[]>([]);

  // Sheet selection state
  const [showSheetSelectionModal, setShowSheetSelectionModal] = useState<boolean>(false);
  const [excelFilesForSheetSelection, setExcelFilesForSheetSelection] = useState<File[]>([]);

  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch existing data sources and combined datasets on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch data sources
        const sources = await fileService.getDataSources();
        setDataSources(sources);

        // Fetch combined datasets
        const combined = await fileService.getCombinedDatasets();
        setCombinedDatasets(combined);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);

  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const newFiles: FileItem[] = [];
    const errors: string[] = [];

    // Process each selected file
    Array.from(selectedFiles).forEach(file => {
      // Check if file is an Excel or CSV file
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const isValidExtension = ['xls', 'xlsx', 'csv'].includes(fileExtension || '');

      if (!isValidExtension) {
        errors.push(`File "${file.name}" is not a valid Excel or CSV file.`);
        return;
      }

      // Add file to the list
      newFiles.push({
        file,
        name: file.name,
        type: fileExtension || '',
        size: file.size
      });
    });

    if (errors.length > 0) {
      setValidationErrors(errors);
    } else {
      setValidationErrors([]);
    }

    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleUpload = async () => {
    if (files.length === 0) {
      setValidationErrors(['Please select at least one file to upload.']);
      return;
    }

    // Check if there are any Excel files that might need sheet selection
    const excelFiles = files
      .filter(fileItem => {
        const fileExtension = fileItem.file.name.split('.').pop()?.toLowerCase();
        return fileExtension === 'xlsx' || fileExtension === 'xls';
      })
      .filter(fileItem => !fileItem.selectedSheet) // Only include files without a selected sheet
      .map(fileItem => fileItem.file);

    // If there are Excel files without selected sheets, show the sheet selection modal
    if (excelFiles.length > 0) {
      setExcelFilesForSheetSelection(excelFiles);
      setShowSheetSelectionModal(true);
      return;
    }

    // If we get here, all files are ready to be uploaded
    await uploadFiles();
  };

  const uploadFiles = async () => {
    // Use the current files array
    uploadFilesWithSheets([...files]);
  };

  // Handle sheet selection completion
  const handleSheetSelectionComplete = (processedFiles: { file: File, name: string, selectedSheet: string }[]) => {
    // Update the files array with the selected sheets
    const updatedFiles = [...files];

    // For each processed file, find the matching file in our state and update it
    processedFiles.forEach(processedFile => {
      const fileIndex = updatedFiles.findIndex(f =>
        f.file.name === processedFile.file.name &&
        f.file.size === processedFile.file.size
      );

      if (fileIndex !== -1) {
        updatedFiles[fileIndex] = {
          ...updatedFiles[fileIndex],
          selectedSheet: processedFile.selectedSheet
        };
      }
    });

    // Update state with the new files array
    setFiles(updatedFiles);

    // Close the modal
    setShowSheetSelectionModal(false);

    // Proceed with upload after a short delay to ensure state is updated
    setTimeout(() => {
      // Use the updated files directly instead of relying on state update
      uploadFilesWithSheets(updatedFiles);
    }, 300);
  };

  // Upload files with sheets
  const uploadFilesWithSheets = async (filesToUpload: FileItem[]) => {
    setIsLoading(true);
    setValidationErrors([]);

    try {
      // Set up progress tracking
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            return 90; // Hold at 90% until complete
          }
          return prev + 5;
        });
      }, 300);

      console.log(`Starting upload of ${filesToUpload.length} files`);

      // Upload each file sequentially
      const uploadedSources: DataSource[] = [];
      for (const fileItem of filesToUpload) {
        try {
          console.log(`Processing file: ${fileItem.file.name}`);

          // Check if this is an Excel file that needs a sheet selection
          const fileExtension = fileItem.file.name.split('.').pop()?.toLowerCase();
          const isExcelFile = fileExtension === 'xlsx' || fileExtension === 'xls';

          console.log(`File extension: ${fileExtension}, Is Excel: ${isExcelFile}`);
          if (isExcelFile) {
            console.log(`Selected sheet: ${fileItem.selectedSheet || 'None'}`);
          }

          // Log the API URL being used
          console.log(`Using API URL: ${import.meta.env.VITE_API_URL || 'http://localhost:8002'}`);

          // Create a FormData object to log what's being sent
          const formData = new FormData();
          formData.append('file', fileItem.file);
          formData.append('name', fileItem.name || fileItem.file.name);
          if (isExcelFile && fileItem.selectedSheet) {
            formData.append('sheet_name', fileItem.selectedSheet);
          }

          console.log('FormData created with:', {
            fileName: fileItem.file.name,
            fileSize: fileItem.file.size,
            name: fileItem.name || fileItem.file.name,
            sheetName: isExcelFile ? fileItem.selectedSheet || 'undefined' : 'N/A'
          });

          // Call the upload file service
          console.log('Calling fileService.uploadFile...');
          const result = await fileService.uploadFile(
            fileItem.file,
            fileItem.name,
            isExcelFile ? fileItem.selectedSheet || undefined : undefined
          );

          console.log('Upload result:', result);

          // Check if the result is a valid DataSource
          if (result && 'requires_sheet_selection' in result) {
            console.error('Sheet selection still required after selection process');
            throw new Error('Sheet selection is required for this Excel file. Please try again.');
          }

          // Check if result is defined before casting
          if (!result) {
            console.error('No response from server for file upload');
            throw new Error('No response from server for file upload');
          }

          // Cast to DataSource and verify it has an ID
          const dataSource = result as DataSource;
          if (!dataSource || !dataSource.id) {
            console.error('Invalid data source response:', dataSource);
            throw new Error('Invalid data source response from server');
          }

          console.log(`Successfully uploaded file: ${fileItem.file.name}, ID: ${dataSource.id}`);
          uploadedSources.push(dataSource);
        } catch (fileError: any) {
          console.error(`Error uploading file ${fileItem.file.name}:`, fileError);
          // Include more details about the error
          const errorDetails = fileError.response ?
            `Status: ${fileError.response.status}, Data: ${JSON.stringify(fileError.response.data)}` :
            fileError.message || 'Unknown error';

          throw new Error(`Error uploading file ${fileItem.file.name}: ${errorDetails}`);
        }
      }

      // If we have multiple files, combine them
      let combinedDatasetId: string | null = null;
      if (uploadedSources.length > 1) {
        // Create a combined dataset name
        const combinedName = `Combined Dataset (${new Date().toLocaleDateString()})`;

        // Combine the datasets
        const sourceIds = uploadedSources.map(source => source.id);

        // Show the combine modal instead of auto-combining
        setSelectedSourceIds(sourceIds);
        setSelectedSources(uploadedSources.map(source => ({
          id: source.id,
          name: source.name
        })));
        setCombinedDatasetName(combinedName);

        // Complete the progress
        clearInterval(interval);
        setUploadProgress(100);

        toast({
          title: "Files uploaded successfully",
          description: `Uploaded ${uploadedSources.length} files. You can now combine them.`,
        });

        // Refresh the data sources list
        const sources = await fileService.getDataSources();
        setDataSources(sources);

        // Show the combine modal
        setShowCombineModal(true);
      } else {
        // Complete the progress
        clearInterval(interval);
        setUploadProgress(100);

        toast({
          title: "File uploaded successfully",
          description: `Uploaded ${uploadedSources[0].name}.`,
        });

        // Navigate to the analysis page
        setTimeout(() => {
          navigate(`/excel-analysis/${uploadedSources[0].id}`);
        }, 1000);
      }
    } catch (error) {
      console.error('Error during file upload:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setValidationErrors([`Error during upload: ${errorMessage}`]);
      setUploadProgress(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle combining datasets with AI-powered schema detection
  const handleCombineDatasets = async (strategy: string, name: string) => {
    if (selectedSourceIds.length < 2 || !name) {
      setValidationErrors(['Please select at least 2 files and provide a name for the combined dataset']);
      return;
    }

    setIsCombining(true);
    try {
      // If using smart strategy, show schema review first
      if (strategy === 'smart') {
        // For each source, get field mappings
        const mappings: any[] = [];
        for (const sourceId of selectedSourceIds) {
          try {
            // Get schema inference for this source
            const schemaResult = await fileService.inferSchema(sourceId);
            if (schemaResult && schemaResult.field_mappings) {
              // Get sample data for this source to enhance the field mappings with sample values
              try {
                const sourceData = await fileService.getDataSourceData(sourceId, 10);
                if (sourceData && sourceData.data && sourceData.data.length > 0) {
                  // Add sample values to each field mapping if not already present
                  schemaResult.field_mappings.forEach((mapping: any) => {
                    if (!mapping.sample_values || mapping.sample_values.length === 0) {
                      const sourceField = mapping.source_field;
                      const sampleValues = sourceData.data
                        .map((row: any) => row[sourceField])
                        .filter((val: any) => val !== undefined && val !== null)
                        .slice(0, 5);
                      mapping.sample_values = sampleValues;
                    }
                  });
                }
              } catch (dataError) {
                console.error(`Error getting sample data for source ${sourceId}:`, dataError);
              }

              mappings.push(...schemaResult.field_mappings);
            }
          } catch (error) {
            console.error(`Error getting schema for source ${sourceId}:`, error);
          }
        }

        if (mappings.length > 0) {
          // Store mappings and show schema review modal
          setFieldMappings(mappings);
          setShowSchemaReviewModal(true);
          setIsCombining(false);
          return;
        }
      }

      // Combine the datasets
      const result = await fileService.combineDatasets(
        selectedSourceIds,
        name,
        {}, // No specific relationships for now
        strategy as any // Cast to the expected type
      );

      // Update the combined datasets list
      setCombinedDatasets(prev => ({
        ...prev,
        [result.id]: result
      }));

      // Close the modal and reset state
      setShowCombineModal(false);
      setSelectedSourceIds([]);
      setSelectedSources([]);
      setCombinedDatasetName('');

      // Show success message
      toast({
        title: strategy === 'smart' ? "AI-powered dataset combination complete" : "Datasets combined successfully",
        description: `Created combined dataset "${name}" with ${result.row_count} rows.`,
      });

      // Navigate to the combined analysis page
      navigate(`/combined-analysis/${result.id}`);
    } catch (error) {
      console.error('Error combining datasets:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setValidationErrors([`Error combining datasets: ${errorMessage}`]);
    } finally {
      setIsCombining(false);
    }
  };

  // Handle updating field mappings
  const handleUpdateFieldMappings = (updatedMappings: any[]) => {
    setFieldMappings(updatedMappings);
  };

  // Handle confirming schema and proceeding with combination
  const handleConfirmSchema = async () => {
    setShowSchemaReviewModal(false);
    setIsCombining(true);

    try {
      // Combine the datasets with the updated field mappings
      const result = await fileService.combineDatasets(
        selectedSourceIds,
        combinedDatasetName,
        {}, // No specific relationships for now
        'smart', // Use smart strategy
        fieldMappings // Pass the field mappings
      );

      // Update the combined datasets list
      setCombinedDatasets(prev => ({
        ...prev,
        [result.id]: result
      }));

      // Reset state
      setSelectedSourceIds([]);
      setSelectedSources([]);
      setCombinedDatasetName('');
      setFieldMappings([]);

      // Show success message
      toast({
        title: "AI-powered dataset combination complete",
        description: `Created combined dataset "${combinedDatasetName}" with ${result.row_count} rows.`,
      });

      // Navigate to the combined analysis page
      navigate(`/combined-analysis/${result.id}`);
    } catch (error) {
      console.error('Error combining datasets:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setValidationErrors([`Error combining datasets: ${errorMessage}`]);
    } finally {
      setIsCombining(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Financial Data Analyzer</h1>
      <p className="text-gray-600 mb-8">
        Upload Excel or CSV files to analyze, clean, and combine your financial data.
        Our AI-powered system will help you identify patterns, detect anomalies, and generate reports.
      </p>

      <div className="grid md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardContent className="pt-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Upload className="mr-2 h-5 w-5 text-blue-500" />
              Upload Files
            </h2>

            <div className="mb-4">
              <Label htmlFor="file-upload">Select Excel or CSV Files</Label>
              <Input
                id="file-upload"
                type="file"
                accept=".xls,.xlsx,.csv"
                onChange={handleFileChange}
                disabled={isLoading}
                className="mt-1"
                multiple
              />
              <p className="text-xs text-gray-500 mt-1">
                You can select multiple files to upload and analyze together
              </p>
            </div>

            {files.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2 text-gray-500">Selected Files</h3>
                <div className="space-y-2 max-h-[200px] overflow-y-auto">
                  {files.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded-md border border-gray-200"
                    >
                      <div className="flex items-center">
                        <FileSpreadsheet size={16} className="mr-2 text-blue-600" />
                        <div>
                          <p className="font-medium text-sm">{file.name}</p>
                          <p className="text-xs text-gray-500">
                            {file.type.toUpperCase()} • {(file.size / 1024).toFixed(2)} KB
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        <X size={14} />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {validationErrors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                <div className="flex items-center text-red-800 mb-1">
                  <AlertCircle size={16} className="mr-2" />
                  <span className="font-medium">Validation errors:</span>
                </div>
                <ul className="list-disc pl-5 text-sm text-red-700">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

            {uploadProgress > 0 && (
              <div className="mb-4">
                <Label className="block mb-1">Upload Progress</Label>
                <Progress value={uploadProgress} className="h-2" />
                <p className="text-sm text-gray-500 mt-1">{uploadProgress}% complete</p>
              </div>
            )}

            <Button
              onClick={handleUpload}
              disabled={isLoading || files.length === 0}
              className="w-full"
            >
              {isLoading ? (
                <>Processing...</>
              ) : (
                <>
                  <Upload size={16} className="mr-2" />
                  Upload and Process
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Brain className="mr-2 h-5 w-5 text-purple-500" />
              AI-Powered Features
            </h2>

            <ul className="space-y-3">
              <li className="flex items-start">
                <div className="bg-purple-100 p-1 rounded-full mr-2 mt-0.5">
                  <FileText size={14} className="text-purple-600" />
                </div>
                <div>
                  <span className="font-medium">Smart Header Detection</span>
                  <p className="text-sm text-gray-600">Automatically identifies the correct header row in your files</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-purple-100 p-1 rounded-full mr-2 mt-0.5">
                  <Database size={14} className="text-purple-600" />
                </div>
                <div>
                  <span className="font-medium">Intelligent Data Combination</span>
                  <p className="text-sm text-gray-600">Combines multiple files with AI-powered schema matching</p>
                </div>
              </li>

              <li className="flex items-start">
                <div className="bg-purple-100 p-1 rounded-full mr-2 mt-0.5">
                  <LineChart size={14} className="text-purple-600" />
                </div>
                <div>
                  <span className="font-medium">Report Generation</span>
                  <p className="text-sm text-gray-600">Creates financial reports and visualizations from your data</p>
                </div>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Previously Uploaded Files */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Your Data</h2>
            {dataSources.length >= 2 && (
              <div className="flex space-x-2">
                <Button
                  onClick={() => {
                    // Navigate to semantic mapping page with all sources
                    const sources = dataSources.map(source => ({
                      id: source.id,
                      name: source.name
                    }));
                    navigate('/semantic-mapping', { state: { sourceIds: sources.map(s => s.id) } });
                  }}
                  variant="outline"
                  className="bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100"
                >
                  <Brain size={16} className="mr-2" />
                  Semantic Mapping
                </Button>
                <Button
                  onClick={() => {
                    // Show a dialog to select a data source for categorization
                    if (dataSources.length > 0) {
                      // For simplicity, just use the first data source
                      // In a real implementation, you would show a dialog to select a data source
                      const sourceId = dataSources[0].id;
                      navigate(`/transaction-categorization/${sourceId}`);
                    } else {
                      toast({
                        title: "No data sources available",
                        description: "Please upload a file first.",
                        variant: "destructive"
                      });
                    }
                  }}
                  variant="outline"
                  className="bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100"
                >
                  <Tag size={16} className="mr-2" />
                  Categorize Transactions
                </Button>
                <Button
                  onClick={() => {
                    // Get selected sources info for the combination panel
                    const sources = dataSources.map(source => ({
                      id: source.id,
                      name: source.name
                    }));
                    setSelectedSources(sources);
                    setSelectedSourceIds(sources.map(s => s.id));
                    setCombinedDatasetName(`Combined Dataset (${new Date().toLocaleDateString()})`);
                    setShowCombineModal(true);
                  }}
                  variant="outline"
                  className="bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100"
                >
                  <Brain size={16} className="mr-2" />
                  Combine All Files
                </Button>
              </div>
            )}
          </div>

          {dataSources.length === 0 && Object.values(combinedDatasets).length === 0 ? (
            <div className="p-6 text-center">
              <Database size={40} className="mx-auto text-gray-300 mb-3" />
              <p className="text-gray-500 mb-2">No data available</p>
              <p className="text-sm text-gray-400">Upload files to get started with your analysis</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Individual Files Section */}
              {dataSources.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Individual Files</h3>
                  <div className="space-y-2">
                    {dataSources.map((dataSource) => (
                      <div
                        key={dataSource.id}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200 hover:bg-gray-100 cursor-pointer"
                        onClick={() => navigate(`/excel-analysis/${dataSource.id}`)}
                      >
                        <div className="flex items-center">
                          <FileSpreadsheet size={20} className="mr-3 text-blue-600" />
                          <div>
                            <p className="font-medium">{dataSource.name}</p>
                            <p className="text-sm text-gray-500">
                              {dataSource.stats && `${dataSource.stats.cleaned_rows} rows`}
                              {dataSource.file_type && ` • ${dataSource.file_type.toUpperCase()}`}
                            </p>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <ArrowRight size={16} />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Combined Datasets Section */}
              {Object.values(combinedDatasets).length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Combined Datasets</h3>
                  <div className="space-y-2">
                    {Object.values(combinedDatasets).map((dataset) => (
                      <div
                        key={dataset.id}
                        className="flex items-center justify-between p-3 bg-purple-50 rounded-md border border-purple-200 hover:bg-purple-100 cursor-pointer"
                        onClick={() => navigate(`/combined-analysis/${dataset.id}`)}
                      >
                        <div className="flex items-center">
                          <Database size={20} className="mr-3 text-purple-600" />
                          <div>
                            <p className="font-medium">{dataset.name}</p>
                            <p className="text-sm text-gray-500">
                              {dataset.row_count} rows •
                              {dataset.source_ids.length} source files
                            </p>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <ArrowRight size={16} />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* AI-Powered Combination Modal */}
      <Dialog open={showCombineModal} onOpenChange={setShowCombineModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>AI-Powered Data Combination</DialogTitle>
          </DialogHeader>
          <CombinationReviewPanel
            sources={selectedSources}
            onCombine={handleCombineDatasets}
            isLoading={isCombining}
          />
        </DialogContent>
      </Dialog>

      {/* Schema Review Modal */}
      <Dialog open={showSchemaReviewModal} onOpenChange={setShowSchemaReviewModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Review AI Schema Detection</DialogTitle>
          </DialogHeader>
          <SchemaReviewPanel
            fieldMappings={fieldMappings}
            onUpdateMapping={handleUpdateFieldMappings}
            onConfirm={handleConfirmSchema}
            isLoading={isCombining}
          />
        </DialogContent>
      </Dialog>

      {/* Sheet Selection Modal */}
      <MultiFileSheetSelector
        isOpen={showSheetSelectionModal}
        onClose={() => setShowSheetSelectionModal(false)}
        files={excelFilesForSheetSelection}
        onComplete={handleSheetSelectionComplete}
      />
    </div>
  );
};

export default DataAnalyzer;
