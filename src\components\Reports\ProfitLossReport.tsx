import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Download, FileText, TrendingUp, TrendingDown } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface ProfitLossReportProps {
  sourceId: string;
}

interface PLLineItem {
  name: string;
  amount: number;
  percentage?: number;
  isSubtotal?: boolean;
  isTotal?: boolean;
  level: number; // 0 = main category, 1 = subcategory, 2 = line item
}

interface PLData {
  revenue: PLLineItem[];
  costOfSales: PLLineItem[];
  operatingExpenses: PLLineItem[];
  totals: {
    totalRevenue: number;
    totalCostOfSales: number;
    grossProfit: number;
    totalOperatingExpenses: number;
    operatingProfit: number;
    netProfit: number;
  };
  period: {
    startDate: string;
    endDate: string;
  };
}

// Mapping from transaction categories to P&L line items
const CATEGORY_TO_PL_MAPPING = {
  // Revenue
  'Income - Fees': 'Sales Income',
  'Income - Revenue': 'Sales Income', 
  'Membership Fee Income': 'Sales Income',
  'Income - Other': 'Other Operating Income',
  'Mentoring Fee Income': 'Other Operating Income',
  
  // Cost of Sales (currently none, but ready for future)
  
  // Operating Expenses
  'Expense - Software': 'Software & IT Services',
  'DataOracle Software Expense': 'Software & IT Services',
  'Expense - Marketing': 'Marketing & Advertising',
  'Marketing Expense': 'Marketing & Advertising',
  'Expense - Events': 'Travel & Entertainment',
  'Workshop and Events Expense': 'Travel & Entertainment',
  'Expense - Transport': 'Travel & Entertainment',
  'Expense - Bank Fees': 'Other Operating Expense',
  'Bank Fees': 'Other Operating Expense',
  'Expense - Office': 'Other Operating Expense',
  'Expense - Other': 'Other Operating Expense',
  'Uncategorized': 'Other Operating Expense'
};

const ProfitLossReport: React.FC<ProfitLossReportProps> = ({ sourceId }) => {
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const [reportName, setReportName] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [plData, setPLData] = useState<PLData | null>(null);
  const [transactions, setTransactions] = useState<any[]>([]);
  const { toast } = useToast();

  // Load transactions when component mounts
  useEffect(() => {
    loadTransactions();
  }, [sourceId]);

  const loadTransactions = async () => {
    try {
      // Try to get categorized transactions first (this is what we want for P&L)
      const response = await fetch(`http://localhost:8002/api/v1/transaction-categorization/export-csv/${sourceId}`);
      if (response.ok) {
        const data = await response.json();
        setTransactions(data);
        console.log('Loaded categorized transactions:', data.length);
        return;
      }

      // If categorized data is not available, show a message
      toast({
        title: 'No categorized data',
        description: 'Please categorize transactions first to generate P&L report.',
        variant: 'destructive'
      });
    } catch (error) {
      console.error('Error loading transactions:', error);
      toast({
        title: 'Error loading data',
        description: 'Could not load transaction data for P&L report. Please ensure transactions are categorized first.',
        variant: 'destructive'
      });
    }
  };

  const generatePLReport = async () => {
    if (!transactions.length) {
      toast({
        title: 'No categorized data available',
        description: 'Please categorize transactions first using the "Categorize Transactions" button in the Data tab.',
        variant: 'destructive'
      });
      return;
    }

    setIsGenerating(true);
    try {
      // Filter transactions by date range if specified
      let filteredTransactions = transactions;
      if (startDate || endDate) {
        filteredTransactions = transactions.filter(transaction => {
          const transactionDate = new Date(
            transaction.transaction_date || 
            transaction.date || 
            transaction.Date ||
            transaction.posting_date ||
            transaction['Transaction Date']
          );
          
          if (startDate && transactionDate < startDate) return false;
          if (endDate && transactionDate > endDate) return false;
          return true;
        });
      }

      console.log(`Processing ${filteredTransactions.length} transactions for P&L`);

      // Process transactions into P&L structure
      const plData = processTransactionsIntoPL(filteredTransactions);
      setPLData(plData);

      toast({
        title: 'P&L Report Generated',
        description: `Report generated with ${filteredTransactions.length} transactions.`,
      });
    } catch (error) {
      console.error('Error generating P&L:', error);
      toast({
        title: 'Error generating report',
        description: 'An error occurred while generating the P&L report.',
        variant: 'destructive'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const processTransactionsIntoPL = (transactions: any[]): PLData => {
    // Initialize line item totals
    const lineItems: { [key: string]: number } = {};
    
    // Process each transaction
    transactions.forEach(transaction => {
      const category = transaction.category || transaction.Category || 'Uncategorized';
      const amount = parseFloat(transaction.amount || transaction.Amount || transaction.value || 0);
      
      // Map category to P&L line item
      const plLineItem = CATEGORY_TO_PL_MAPPING[category] || 'Other Operating Expense';
      
      // Accumulate amounts
      lineItems[plLineItem] = (lineItems[plLineItem] || 0) + amount;
    });

    // Build P&L structure
    const revenue: PLLineItem[] = [
      { name: 'Sales Income', amount: lineItems['Sales Income'] || 0, level: 1 },
      { name: 'Other Operating Income', amount: lineItems['Other Operating Income'] || 0, level: 1 }
    ];

    const costOfSales: PLLineItem[] = [
      // Currently no cost of sales categories, but structure is ready
    ];

    const operatingExpenses: PLLineItem[] = [
      { name: 'Personnel Expense', amount: lineItems['Personnel Expense'] || 0, level: 1 },
      { name: 'Marketing & Advertising', amount: lineItems['Marketing & Advertising'] || 0, level: 1 },
      { name: 'Sales Commissions', amount: lineItems['Sales Commissions'] || 0, level: 1 },
      { name: 'Rent & Facilities', amount: lineItems['Rent & Facilities'] || 0, level: 1 },
      { name: 'Utilities', amount: lineItems['Utilities'] || 0, level: 1 },
      { name: 'Software & IT Services', amount: lineItems['Software & IT Services'] || 0, level: 1 },
      { name: 'Cloud Hosting', amount: lineItems['Cloud Hosting'] || 0, level: 1 },
      { name: 'Depreciation & Amortisation', amount: lineItems['Depreciation & Amortisation'] || 0, level: 1 },
      { name: 'Travel & Entertainment', amount: lineItems['Travel & Entertainment'] || 0, level: 1 },
      { name: 'Professional & Legal Services', amount: lineItems['Professional & Legal Services'] || 0, level: 1 },
      { name: 'Insurance', amount: lineItems['Insurance'] || 0, level: 1 },
      { name: 'Research & Development', amount: lineItems['Research & Development'] || 0, level: 1 },
      { name: 'Other Operating Expense', amount: lineItems['Other Operating Expense'] || 0, level: 1 }
    ];

    // Calculate totals
    const totalRevenue = revenue.reduce((sum, item) => sum + Math.abs(item.amount), 0);
    const totalCostOfSales = costOfSales.reduce((sum, item) => sum + Math.abs(item.amount), 0);
    const grossProfit = totalRevenue - totalCostOfSales;
    const totalOperatingExpenses = operatingExpenses.reduce((sum, item) => sum + Math.abs(item.amount), 0);
    const operatingProfit = grossProfit - totalOperatingExpenses;
    const netProfit = operatingProfit; // No tax calculation for now

    return {
      revenue,
      costOfSales,
      operatingExpenses,
      totals: {
        totalRevenue,
        totalCostOfSales,
        grossProfit,
        totalOperatingExpenses,
        operatingProfit,
        netProfit
      },
      period: {
        startDate: startDate ? format(startDate, 'yyyy-MM-dd') : '',
        endDate: endDate ? format(endDate, 'yyyy-MM-dd') : ''
      }
    };
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(Math.abs(amount));
  };

  const exportToPDF = () => {
    // TODO: Implement PDF export
    toast({
      title: 'Export feature',
      description: 'PDF export will be implemented in the next phase.',
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Profit & Loss Report
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Report Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label htmlFor="reportName">Report Name</Label>
            <Input
              id="reportName"
              placeholder="P&L Report"
              value={reportName}
              onChange={(e) => setReportName(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label>Start Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !startDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {startDate ? format(startDate, "PPP") : "Select start date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={setStartDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label>End Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !endDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {endDate ? format(endDate, "PPP") : "Select end date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={setEndDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label>&nbsp;</Label>
            <Button 
              onClick={generatePLReport} 
              disabled={isGenerating}
              className="w-full"
            >
              {isGenerating ? 'Generating...' : 'Generate P&L'}
            </Button>
          </div>
        </div>

        {/* No Data Message */}
        {!transactions.length && (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Transaction Data Available</h3>
            <p className="text-gray-500 mb-4">
              To generate a P&L report, you need to first categorize your transactions.
            </p>
            <p className="text-sm text-gray-400">
              Go to the <strong>Data</strong> tab and click <strong>"Categorize Transactions"</strong> to get started.
            </p>
          </div>
        )}

        {/* P&L Report Display */}
        {plData && transactions.length > 0 && (
          <div className="space-y-6">
            {/* Report Header */}
            <div className="text-center border-b pb-4">
              <h2 className="text-2xl font-bold">PROFIT & LOSS STATEMENT</h2>
              <p className="text-gray-600">
                {plData.period.startDate && plData.period.endDate 
                  ? `For the Period: ${format(new Date(plData.period.startDate), 'MMM dd, yyyy')} to ${format(new Date(plData.period.endDate), 'MMM dd, yyyy')}`
                  : 'All Transactions'
                }
              </p>
            </div>

            {/* Export Actions */}
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={exportToPDF}>
                <Download className="mr-2 h-4 w-4" />
                Export PDF
              </Button>
            </div>

            {/* P&L Table */}
            <div className="space-y-4">
              {/* Revenue Section */}
              <div>
                <h3 className="text-lg font-semibold mb-2">REVENUE</h3>
                <div className="space-y-1">
                  {plData.revenue.map((item, index) => (
                    item.amount !== 0 && (
                      <div key={index} className="flex justify-between items-center py-1 px-2">
                        <span className="ml-4">{item.name}</span>
                        <span>{formatCurrency(item.amount)}</span>
                      </div>
                    )
                  ))}
                  <div className="flex justify-between items-center py-2 px-2 border-t font-semibold">
                    <span>TOTAL REVENUE</span>
                    <span>{formatCurrency(plData.totals.totalRevenue)}</span>
                  </div>
                </div>
              </div>

              {/* Cost of Sales Section */}
              {plData.totals.totalCostOfSales > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">COST OF SALES</h3>
                  <div className="space-y-1">
                    {plData.costOfSales.map((item, index) => (
                      item.amount !== 0 && (
                        <div key={index} className="flex justify-between items-center py-1 px-2">
                          <span className="ml-4">{item.name}</span>
                          <span>({formatCurrency(item.amount)})</span>
                        </div>
                      )
                    ))}
                    <div className="flex justify-between items-center py-2 px-2 border-t font-semibold">
                      <span>TOTAL COST OF SALES</span>
                      <span>({formatCurrency(plData.totals.totalCostOfSales)})</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Gross Profit */}
              <div className="flex justify-between items-center py-2 px-2 bg-blue-50 font-semibold text-lg">
                <span>GROSS PROFIT</span>
                <span className="flex items-center">
                  {plData.totals.grossProfit >= 0 ? (
                    <TrendingUp className="mr-1 h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="mr-1 h-4 w-4 text-red-600" />
                  )}
                  {formatCurrency(plData.totals.grossProfit)}
                </span>
              </div>

              {/* Operating Expenses Section */}
              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">OPERATING EXPENSES</h3>
                <div className="space-y-1">
                  {plData.operatingExpenses.map((item, index) => (
                    item.amount !== 0 && (
                      <div key={index} className="flex justify-between items-center py-1 px-2">
                        <span className="ml-4">{item.name}</span>
                        <span>({formatCurrency(item.amount)})</span>
                      </div>
                    )
                  ))}
                  <div className="flex justify-between items-center py-2 px-2 border-t font-semibold">
                    <span>TOTAL OPERATING EXPENSES</span>
                    <span>({formatCurrency(plData.totals.totalOperatingExpenses)})</span>
                  </div>
                </div>
              </div>

              {/* Operating Profit (EBIT) */}
              <div className="flex justify-between items-center py-2 px-2 bg-green-50 font-semibold text-lg">
                <span>OPERATING PROFIT (EBIT)</span>
                <span className="flex items-center">
                  {plData.totals.operatingProfit >= 0 ? (
                    <TrendingUp className="mr-1 h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="mr-1 h-4 w-4 text-red-600" />
                  )}
                  {formatCurrency(plData.totals.operatingProfit)}
                </span>
              </div>

              {/* Net Profit */}
              <div className="mt-4 space-y-2">
                <div className="flex justify-between items-center py-1 px-2">
                  <span className="ml-4">Income Tax Expense</span>
                  <span>$0.00</span>
                </div>
                <div className="flex justify-between items-center py-3 px-2 bg-gray-100 font-bold text-xl border-t-2 border-gray-300">
                  <span>NET PROFIT (LOSS)</span>
                  <span className={`flex items-center ${plData.totals.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {plData.totals.netProfit >= 0 ? (
                      <TrendingUp className="mr-1 h-5 w-5" />
                    ) : (
                      <TrendingDown className="mr-1 h-5 w-5" />
                    )}
                    {formatCurrency(plData.totals.netProfit)}
                  </span>
                </div>
              </div>

              {/* Key Metrics Summary */}
              <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Gross Margin</p>
                      <p className="text-2xl font-bold">
                        {plData.totals.totalRevenue > 0
                          ? `${((plData.totals.grossProfit / plData.totals.totalRevenue) * 100).toFixed(1)}%`
                          : '0%'
                        }
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Operating Margin</p>
                      <p className="text-2xl font-bold">
                        {plData.totals.totalRevenue > 0
                          ? `${((plData.totals.operatingProfit / plData.totals.totalRevenue) * 100).toFixed(1)}%`
                          : '0%'
                        }
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Net Margin</p>
                      <p className="text-2xl font-bold">
                        {plData.totals.totalRevenue > 0
                          ? `${((plData.totals.netProfit / plData.totals.totalRevenue) * 100).toFixed(1)}%`
                          : '0%'
                        }
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProfitLossReport;
