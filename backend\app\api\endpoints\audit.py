import logging
import os
import sys
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session

from app.api.dependencies import get_current_user, get_db
from app.models.audit import AuditLog, RecordHistory
from app.models.user import User
from app.schemas.audit import AuditLogCreate, AuditLogResponse, RecordHistoryCreate, RecordHistoryResponse

# Import for backward compatibility with excel_api.py
try:
    # Add the parent directory to sys.path to import from excel_api.py
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
    from excel_api import AUDIT_LOGS
    EXCEL_API_AVAILABLE = True
except ImportError:
    EXCEL_API_AVAILABLE = False
    AUDIT_LOGS = []

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/log", response_model=AuditLogResponse)
async def create_audit_log(
    log: AuditLogCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Create an audit log entry.
    """
    try:
        # Get client IP
        client_ip = request.client.host if request.client else None

        # Create audit log
        audit_log = AuditLog(
            action=log.action,
            description=log.description,
            category=log.category,
            details=log.details,
            ip_address=client_ip,
            user_id=current_user.id,
        )

        db.add(audit_log)
        db.commit()
        db.refresh(audit_log)

        # Create record history entries if provided
        if log.record_history:
            for record in log.record_history:
                record_history = RecordHistory(
                    record_type=record.record_type,
                    record_id=record.record_id,
                    field=record.field,
                    old_value=record.old_value,
                    new_value=record.new_value,
                    reason=record.reason,
                    audit_log_id=audit_log.id,
                )

                db.add(record_history)

            db.commit()

        return audit_log
    except Exception as e:
        logger.error(f"Error creating audit log: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating audit log: {str(e)}",
        )


@router.get("/logs", response_model=List[AuditLogResponse])
async def get_audit_logs(
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    user_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Get audit logs with optional filtering.
    """
    # Build query
    query = db.query(AuditLog)

    # Apply filters
    if category:
        query = query.filter(AuditLog.category == category)

    if user_id:
        query = query.filter(AuditLog.user_id == user_id)

    # Order by created_at descending
    query = query.order_by(AuditLog.created_at.desc())

    # Apply pagination
    logs = query.offset(skip).limit(limit).all()

    return logs


@router.get("/logs/{log_id}", response_model=AuditLogResponse)
async def get_audit_log(
    log_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Get a specific audit log.
    """
    log = db.query(AuditLog).filter(AuditLog.id == log_id).first()

    if not log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Audit log not found",
        )

    return log


@router.get("/history/{record_type}/{record_id}", response_model=List[RecordHistoryResponse])
async def get_record_history(
    record_type: str,
    record_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Get history for a specific record.
    """
    history = db.query(RecordHistory).filter(
        RecordHistory.record_type == record_type,
        RecordHistory.record_id == record_id
    ).order_by(RecordHistory.created_at.desc()).all()

    return history


@router.get("/excel-logs")
async def get_excel_audit_logs(
    source_id: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Get audit logs from excel_api.py, optionally filtered by source ID.
    This endpoint is for backward compatibility with excel_api.py.
    """
    try:
        # For now, we'll use the excel_api.py implementation for audit logs
        if EXCEL_API_AVAILABLE:
            if source_id:
                return [log for log in AUDIT_LOGS if log.get("details", {}).get("source_id") == source_id]
            return AUDIT_LOGS

        # If excel_api.py is not available, implement a simple version here
        # This is a placeholder for future implementation

        # For now, we'll just return a mock response
        return []
    except Exception as e:
        logger.error(f"Error getting audit logs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting audit logs: {str(e)}",
        )
