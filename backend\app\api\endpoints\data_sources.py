import logging
import os
import uuid
import json
import sys
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from io import BytesIO
from pydantic import BaseModel

from app.api.dependencies import get_current_user, get_db
from app.models.data_source import DataSource
from app.models.user import User
from app.utils.storage_factory import storage_client

# Import for Excel sheet handling
import pandas as pd
import io

# Import for backward compatibility with excel_api.py
try:
    # Add the parent directory to sys.path to import from excel_api.py
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
    from excel_api import DATA_SOURCES, COMBINED_DATASETS
    EXCEL_API_AVAILABLE = True
except ImportError:
    EXCEL_API_AVAILABLE = False
    DATA_SOURCES = []
    COMBINED_DATASETS = {}

# Schema for field mapping
class FieldMapping(BaseModel):
    target_field: str
    source_fields: Dict[str, str]
    transformation: Optional[str] = None

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/", response_model=List[Dict[str, Any]])
async def get_data_sources(
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Get all data sources.
    """
    try:
        data_sources = db.query(DataSource).all()
        return [
            {
                "id": ds.id,
                "name": ds.name,
                "description": ds.description,
                "source_type": ds.source_type,
                "file_path": ds.file_path,
                "file_type": ds.file_type,
                "schema": ds.schema,
                "record_count": ds.record_count if hasattr(ds, "record_count") else 0,
                "created_at": ds.created_at.isoformat(),
                "updated_at": ds.updated_at.isoformat(),
            }
            for ds in data_sources
        ]
    except Exception as e:
        logger.error(f"Error getting data sources: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting data sources: {str(e)}",
        )


@router.get("/{data_source_id}", response_model=Dict[str, Any])
async def get_data_source(
    data_source_id: str,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Get a data source by ID.
    """
    try:
        # Try to convert to int for database query
        try:
            data_source_id_int = int(data_source_id)
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id_int).first()
        except ValueError:
            # If not an integer, it might be a string ID
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()

        if not data_source:
            # Try to find it in excel_api.py DATA_SOURCES
            if EXCEL_API_AVAILABLE:
                for source in DATA_SOURCES:
                    if source["id"] == data_source_id:
                        # Add stats object if not present
                        if "stats" not in source:
                            # Get data from the JSON file
                            with open(source["json_path"], "r") as f:
                                data = json.load(f)

                            # Add stats object
                            source["stats"] = {
                                "total_rows": len(data),
                                "cleaned_rows": len(data),
                                "columns": list(data[0].keys()) if data else [],
                                "duplicates_removed": 0,
                                "file_type": source.get("file_type", "csv")
                            }

                        return source

            # If not found anywhere, return 404
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Data source with ID {data_source_id} not found",
            )

        # Convert to dict for response
        data_source_dict = {
            "id": str(data_source.id),
            "name": data_source.name,
            "description": data_source.description,
            "source_type": data_source.source_type,
            "file_path": data_source.file_path,
            "file_type": data_source.file_type,
            "schema": data_source.schema,  # Use schema for backward compatibility
            "record_count": data_source.record_count if hasattr(data_source, "record_count") else 0,
            "created_at": data_source.created_at.isoformat(),
            "updated_at": data_source.updated_at.isoformat(),
        }

        # Add stats object if not present
        if "stats" not in data_source_dict:
            # Get mock data for now
            # In a real implementation, this would fetch data from storage
            import random

            # Create sample data with 200 rows
            sample_data = []
            for i in range(200):
                sample_data.append({
                    "id": i,
                    "name": f"Item {i}",
                    "value": random.randint(100, 10000) / 100,
                    "date": f"2023-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}",
                    "category": random.choice(["A", "B", "C", "D"]),
                    "status": random.choice(["Active", "Inactive", "Pending"]),
                })

            # Add stats object
            data_source_dict["stats"] = {
                "total_rows": len(sample_data),
                "cleaned_rows": len(sample_data),
                "columns": list(sample_data[0].keys()),
                "duplicates_removed": 0,
                "file_type": data_source.source_type or "csv"
            }

        return data_source_dict
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting data source: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting data source: {str(e)}",
        )


@router.post("/", response_model=Dict[str, Any])
async def create_data_source(
    data_source: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Create a new data source.
    """
    try:
        # Create data source record
        new_data_source = DataSource(
            name=data_source.get("name", "Unnamed Data Source"),
            description=data_source.get("description"),
            source_type=data_source.get("source_type", "file"),
            file_type=data_source.get("file_type"),
            schema=data_source.get("schema", {}),
            connection_details=data_source.get("connection_details"),
            file_path=data_source.get("file_path"),
            owner_id=current_user.id,
        )

        db.add(new_data_source)
        db.commit()
        db.refresh(new_data_source)

        return {
            "id": new_data_source.id,
            "name": new_data_source.name,
            "description": new_data_source.description,
            "source_type": new_data_source.source_type,
            "file_path": new_data_source.file_path,
            "file_type": new_data_source.file_type,
            "schema": new_data_source.schema,  # Use schema for backward compatibility
            "record_count": 0,
            "created_at": new_data_source.created_at.isoformat(),
            "updated_at": new_data_source.updated_at.isoformat(),
        }
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating data source: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating data source: {str(e)}",
        )


@router.post("/upload", response_model=Dict[str, Any])
async def upload_file(
    file: UploadFile = File(...),
    name: str = Form(None),
    description: str = Form(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Upload a file to create a data source.
    """
    try:
        # Generate a unique file name
        file_extension = os.path.splitext(file.filename)[1] if file.filename else ""
        unique_filename = f"{uuid.uuid4()}{file_extension}"

        # Upload file to storage
        destination_path = f"uploads/{unique_filename}"
        storage_client.upload_file(file.file, destination_path)

        # Create data source record
        data_source = DataSource(
            name=name or file.filename,
            description=description,
            source_type="file",
            file_type=file_extension.lstrip("."),
            file_path=destination_path,
            schema={},
            owner_id=current_user.id,
        )

        db.add(data_source)
        db.commit()
        db.refresh(data_source)

        return {
            "id": data_source.id,
            "name": data_source.name,
            "description": data_source.description,
            "source_type": data_source.source_type,
            "file_path": data_source.file_path,
            "file_type": data_source.file_type,
            "schema": data_source.schema,  # Use schema for backward compatibility
            "record_count": 0,
            "created_at": data_source.created_at.isoformat(),
            "updated_at": data_source.updated_at.isoformat(),
        }
    except Exception as e:
        db.rollback()
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error uploading file: {str(e)}",
        )


@router.post("/from-data", response_model=Dict[str, Any])
async def create_from_data(
    request: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Create a data source from parsed data.
    """
    try:
        # Extract data from request
        name = request.get("name", "Unnamed Data Source")
        description = request.get("description")
        source_type = request.get("source_type", "file")
        file_type = request.get("file_type", "csv")
        data = request.get("data", [])
        schema = request.get("schema", {})

        # Create a unique file name
        unique_filename = f"{uuid.uuid4()}.{file_type}"
        destination_path = f"uploads/{unique_filename}"

        # Create data source record
        data_source = DataSource(
            name=name,
            description=description,
            source_type=source_type,
            file_type=file_type,
            file_path=destination_path,
            schema=schema,
            owner_id=current_user.id,
        )

        # Add record count if data is provided
        if hasattr(data_source, "record_count") and data:
            data_source.record_count = len(data)

        db.add(data_source)
        db.commit()
        db.refresh(data_source)

        return {
            "id": data_source.id,
            "name": data_source.name,
            "description": data_source.description,
            "source_type": data_source.source_type,
            "file_path": data_source.file_path,
            "file_type": data_source.file_type,
            "schema": data_source.schema,  # Use schema for backward compatibility
            "record_count": len(data) if data else 0,
            "created_at": data_source.created_at.isoformat(),
            "updated_at": data_source.updated_at.isoformat(),
        }
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating data source from data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating data source from data: {str(e)}",
        )


@router.post("/get-excel-sheets")
async def get_excel_sheets(
    file: UploadFile = File(...),
):
    """
    Get the sheet names from an Excel file.
    """
    try:
        # Read the Excel file
        contents = await file.read()

        # Use pandas to read the Excel file and get sheet names
        excel_file = pd.ExcelFile(io.BytesIO(contents))
        sheet_names = excel_file.sheet_names

        # Create a simple response without previews to avoid serialization issues
        return {
            "sheet_names": sheet_names,
            "default_sheet": sheet_names[0] if sheet_names else ""
        }
    except Exception as e:
        logger.error(f"Error getting Excel sheets: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting Excel sheets: {str(e)}",
        )


@router.get("/{data_source_id}/data")
async def get_data_source_data(
    data_source_id: str,
    limit: int = 100,
    full: bool = False,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Get the data for a specific data source.
    """
    try:
        # For now, we'll implement a simple mock response
        # In a real implementation, this would fetch data from storage

        # Check if the data source exists
        try:
            data_source_id_int = int(data_source_id)
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id_int).first()
        except ValueError:
            # If the ID is not an integer, try to find it by string ID
            # This is for backward compatibility with the excel_api.py implementation
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()

        if not data_source:
            # Try to find the data source in the excel_api.py DATA_SOURCES list
            # This is a temporary solution for backward compatibility
            try:
                # Import the DATA_SOURCES from excel_api.py
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
                from excel_api import DATA_SOURCES

                # Find the data source
                excel_data_source = None
                for source in DATA_SOURCES:
                    if source["id"] == data_source_id:
                        excel_data_source = source
                        break

                if excel_data_source:
                    # Load the data from the JSON file
                    json_path = excel_data_source["json_path"]
                    with open(json_path, "r") as f:
                        data = json.load(f)

                    # Return full dataset if requested, otherwise limit rows
                    if full:
                        return {"data": data, "total": len(data)}
                    else:
                        return {"data": data[:limit], "total": len(data)}
            except Exception as e:
                logger.error(f"Error loading data from excel_api.py: {e}")
                pass

            # If we get here, the data source was not found
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Data source with ID {data_source_id} not found",
            )

        # Generate mock data for now
        # In a real implementation, this would fetch data from storage
        import random

        # Create sample data with 200 rows
        sample_data = []
        for i in range(200):
            sample_data.append({
                "id": i,
                "name": f"Item {i}",
                "value": random.randint(100, 10000) / 100,
                "date": f"2023-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}",
                "category": random.choice(["A", "B", "C", "D"]),
                "status": random.choice(["Active", "Inactive", "Pending"]),
            })

        # Return full dataset if requested, otherwise limit rows
        if full:
            return {"data": sample_data, "total": len(sample_data)}
        else:
            return {"data": sample_data[:limit], "total": len(sample_data)}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting data source data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting data source data: {str(e)}",
        )


@router.post("/combine")
async def combine_datasets(
    request: Dict[str, Any],
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Combine multiple datasets into a single dataset with AI-powered schema matching.
    """
    try:
        # Extract request parameters
        source_ids = request.get("source_ids", [])
        name = request.get("name", "Combined Dataset")
        # relationships = request.get("relationships", {})  # Unused variable
        join_strategy = request.get("join_strategy", "smart")
        field_mappings = request.get("field_mappings", [])

        if not source_ids or len(source_ids) < 2:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least two data source IDs are required for combining datasets",
            )

        # For now, we'll use the excel_api.py implementation for combining datasets
        if EXCEL_API_AVAILABLE:
            # Import the combine_datasets function from excel_api.py
            from excel_api import combine_datasets as excel_combine_datasets

            # Call the function
            result = await excel_combine_datasets(request)

            return result

        # If excel_api.py is not available, implement a simple version here
        # This is a placeholder for future implementation

        # Find the data sources
        sources = []
        for source_id in source_ids:
            try:
                # Try to convert to int for database query
                source_id_int = int(source_id)
                source = db.query(DataSource).filter(DataSource.id == source_id_int).first()
            except ValueError:
                # If not an integer, it might be a string ID
                source = db.query(DataSource).filter(DataSource.id == source_id).first()

            if not source:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Data source with ID {source_id} not found",
                )

            sources.append(source)

        # Create a combined dataset
        combined_id = str(uuid.uuid4())

        # For now, we'll just return a mock response
        return {
            "id": combined_id,
            "name": name,
            "source_ids": source_ids,
            "join_strategy": join_strategy,
            "field_mappings": field_mappings,
            "created_at": datetime.now().isoformat(),
            "status": "completed",
            "record_count": 0,
            "message": "Combined dataset created successfully",
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error combining datasets: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error combining datasets: {str(e)}",
        )


@router.get("/combined/{combined_id}")
async def get_combined_dataset(
    combined_id: str,
    # db: Session = Depends(get_db),  # Unused parameter
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Get a specific combined dataset.
    """
    try:
        # For now, we'll use the excel_api.py implementation for combined datasets
        if EXCEL_API_AVAILABLE:
            if combined_id in COMBINED_DATASETS:
                return COMBINED_DATASETS[combined_id]

        # If not found or excel_api.py is not available, return 404
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Combined dataset with ID {combined_id} not found",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting combined dataset: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting combined dataset: {str(e)}",
        )


@router.get("/combined/{combined_id}/data")
async def get_combined_dataset_data(
    combined_id: str,
    limit: int = 100,
    # db: Session = Depends(get_db),  # Unused parameter
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Get the data for a combined dataset.
    """
    try:
        # For now, we'll use the excel_api.py implementation for combined datasets
        if EXCEL_API_AVAILABLE:
            if combined_id in COMBINED_DATASETS:
                combined_dataset = COMBINED_DATASETS[combined_id]

                # Load the data from the JSON file
                with open(combined_dataset["json_path"], "r") as f:
                    data = json.load(f)

                return {"data": data[:limit], "total": len(data)}

        # If not found or excel_api.py is not available, return 404
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Combined dataset with ID {combined_id} not found",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting combined dataset data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting combined dataset data: {str(e)}",
        )


@router.post("/reprocess/{dataset_id}")
async def reprocess_dataset(
    dataset_id: str,
    request: Dict[str, Any],
    # db: Session = Depends(get_db),  # Unused parameter
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Reprocess a combined dataset - delete and recreate it with the same sources.
    """
    try:
        # For now, we'll use the excel_api.py implementation for combined datasets
        if EXCEL_API_AVAILABLE:
            # Import the reprocess_dataset function from excel_api.py
            from excel_api import reprocess_dataset as excel_reprocess_dataset

            # Call the function
            result = await excel_reprocess_dataset(dataset_id, request)

            return result

        # If excel_api.py is not available, return 404
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Combined dataset with ID {dataset_id} not found",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reprocessing dataset: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error reprocessing dataset: {str(e)}",
        )


@router.post("/analyze-schemas")
async def analyze_schemas(
    request: Dict[str, Any],
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Analyze schemas from multiple data sources and suggest mappings.
    """
    try:
        # Extract request parameters
        source_ids = request.get("source_ids", [])

        if not source_ids or len(source_ids) < 2:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least two data source IDs are required for schema analysis",
            )

        # For now, we'll use the excel_api.py implementation for schema analysis
        if EXCEL_API_AVAILABLE:
            # Import the analyze_schemas function from excel_api.py
            from excel_api import analyze_schemas as excel_analyze_schemas

            # Call the function
            result = await excel_analyze_schemas(request)

            return result

        # If excel_api.py is not available, implement a simple version here
        # This is a placeholder for future implementation

        # Find the data sources
        sources = []
        for source_id in source_ids:
            try:
                # Try to convert to int for database query
                source_id_int = int(source_id)
                source = db.query(DataSource).filter(DataSource.id == source_id_int).first()
            except ValueError:
                # If not an integer, it might be a string ID
                source = db.query(DataSource).filter(DataSource.id == source_id).first()

            if not source:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Data source with ID {source_id} not found",
                )

            sources.append(source)

        # For now, we'll just return a mock response
        return {
            "source_schemas": [
                {
                    "source_id": source.id,
                    "source_name": source.name,
                    "source_index": i,
                    "columns": [],
                    "column_types": {},
                    "sample_values": {}
                }
                for i, source in enumerate(sources)
            ],
            "field_mappings": []
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing schemas: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error analyzing schemas: {str(e)}",
        )


@router.post("/preview-combined-data")
async def preview_combined_data(
    request: Dict[str, Any],
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Generate a preview of combined data based on field mappings.
    """
    try:
        # Extract request parameters
        source_ids = request.get("source_ids", [])
        mappings = request.get("mappings", [])
        # limit = request.get("limit", 5)  # Unused variable

        if not source_ids or len(source_ids) < 2:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least two data source IDs are required for data preview",
            )

        if not mappings:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No field mappings provided",
            )

        # For now, we'll use the excel_api.py implementation for data preview
        if EXCEL_API_AVAILABLE:
            # Import the preview_combined_data_endpoint function from excel_api.py
            from excel_api import preview_combined_data_endpoint as excel_preview_combined_data

            # Call the function
            result = await excel_preview_combined_data(request)

            return result

        # If excel_api.py is not available, implement a simple version here
        # This is a placeholder for future implementation

        # Find the data sources
        sources = []
        for source_id in source_ids:
            try:
                # Try to convert to int for database query
                source_id_int = int(source_id)
                source = db.query(DataSource).filter(DataSource.id == source_id_int).first()
            except ValueError:
                # If not an integer, it might be a string ID
                source = db.query(DataSource).filter(DataSource.id == source_id).first()

            if not source:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Data source with ID {source_id} not found",
                )

            sources.append(source)

        # For now, we'll just return a mock response
        return {
            "preview_data": [],
            "total_rows": 0,
            "columns": [],
            "message": "Preview generated successfully",
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating preview: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating preview: {str(e)}",
        )


@router.post("/analyze/{dataset_id}")
async def analyze_dataset(
    dataset_id: str,
    request: Dict[str, Any],
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Perform advanced analysis on a dataset.
    """
    try:
        # Get analysis type
        analysis_type = request.get("type", "general")
        columns = request.get("columns", [])

        # Check if it's a combined dataset in excel_api.py
        if EXCEL_API_AVAILABLE and dataset_id in COMBINED_DATASETS:
            # Get the dataset from COMBINED_DATASETS
            # dataset = COMBINED_DATASETS[dataset_id]  # Unused variable

            # Import the analyze_dataset function from excel_api.py
            from excel_api import analyze_dataset as excel_analyze_dataset

            # Call the function
            result = await excel_analyze_dataset(dataset_id, request)

            return result

        # If not a combined dataset, check if it's an individual data source
        try:
            # Try to convert to int for database query
            dataset_id_int = int(dataset_id)
            data_source = db.query(DataSource).filter(DataSource.id == dataset_id_int).first()
        except ValueError:
            # If not an integer, it might be a string ID
            data_source = db.query(DataSource).filter(DataSource.id == dataset_id).first()

        if not data_source:
            # Try to find it in excel_api.py DATA_SOURCES
            if EXCEL_API_AVAILABLE:
                for source in DATA_SOURCES:
                    if source["id"] == dataset_id:
                        # Import the analyze_dataset function from excel_api.py
                        from excel_api import analyze_dataset as excel_analyze_dataset

                        # Call the function
                        result = await excel_analyze_dataset(dataset_id, request)

                        return result

            # If not found anywhere, return 404
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Dataset with ID {dataset_id} not found",
            )

        # For now, we'll just return a mock response
        return {
            "dataset_id": dataset_id,
            "analysis_type": analysis_type,
            "columns_analyzed": columns,
            "results": {},
            "message": "Analysis completed successfully",
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing dataset: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error analyzing dataset: {str(e)}",
        )


@router.post("/summarize/{data_source_id}")
async def summarize_dataset(
    data_source_id: str,
    request: Dict[str, Any] = {},
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Generate a summary of a dataset with statistics for each column.
    """
    try:
        # Check if it's a combined dataset
        if EXCEL_API_AVAILABLE and data_source_id in COMBINED_DATASETS:
            # Import the summarize_dataset function from excel_api.py
            from excel_api import summarize_dataset as excel_summarize_dataset

            # Call the function
            result = await excel_summarize_dataset(data_source_id, request)

            return result

        # If not a combined dataset, check if it's an individual data source
        try:
            # Try to convert to int for database query
            data_source_id_int = int(data_source_id)
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id_int).first()
        except ValueError:
            # If not an integer, it might be a string ID
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()

        if not data_source:
            # Try to find it in excel_api.py DATA_SOURCES
            if EXCEL_API_AVAILABLE:
                for source in DATA_SOURCES:
                    if source["id"] == data_source_id:
                        # Import the summarize_dataset function from excel_api.py
                        from excel_api import summarize_dataset as excel_summarize_dataset

                        # Call the function
                        result = await excel_summarize_dataset(data_source_id, request)

                        return result

            # If not found anywhere, return 404
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Dataset with ID {data_source_id} not found",
            )

        # Get the data
        # For now, we'll use mock data
        import random
        # import numpy as np  # Unused import

        # Create sample data with 200 rows
        sample_data = []
        for i in range(200):
            sample_data.append({
                "id": i,
                "name": f"Item {i}",
                "value": random.randint(100, 10000) / 100,
                "date": f"2023-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}",
                "category": random.choice(["A", "B", "C", "D"]),
                "status": random.choice(["Active", "Inactive", "Pending"]),
            })

        # Convert to DataFrame
        df = pd.DataFrame(sample_data)

        # Get all column names
        columns = list(df.columns)

        # Generate summary in the format expected by the frontend
        # This matches the DataSource.stats structure in excelService.ts
        summary = {
            "dataset_id": data_source_id,
            "dataset_name": data_source.name,
            "row_count": len(df),
            "column_count": len(df.columns),
            "is_combined": False,
            "numerical_columns": {},
            "categorical_columns": {},
            "date_columns": [],
            "missing_values": {},
            "top_correlations": [],
            # Add the stats object that matches the frontend's expected structure
            "stats": {
                "total_rows": len(df),
                "cleaned_rows": len(df),
                "columns": columns,
                "duplicates_removed": 0,
                "file_type": data_source.file_type if hasattr(data_source, 'file_type') else "csv"
            }
        }

        # Analyze each column
        for column in df.columns:
            # Check if column is numeric
            if pd.api.types.is_numeric_dtype(df[column]):
                # Calculate statistics for numerical columns
                summary["numerical_columns"][column] = {
                    "min": float(df[column].min()),
                    "max": float(df[column].max()),
                    "mean": float(df[column].mean()),
                    "median": float(df[column].median()),
                    "std": float(df[column].std()),
                    "missing": int(df[column].isna().sum())
                }

                # Calculate correlations with other numerical columns
                for other_column in df.columns:
                    if other_column != column and pd.api.types.is_numeric_dtype(df[other_column]):
                        correlation = df[column].corr(df[other_column])
                        if not pd.isna(correlation) and abs(correlation) > 0.5:
                            summary["top_correlations"].append({
                                "column1": column,
                                "column2": other_column,
                                "correlation": float(correlation)
                            })

            # Check if column might be a date
            elif column.lower().find("date") >= 0 or column.lower().find("time") >= 0:
                summary["date_columns"].append(column)
            # Otherwise treat as categorical
            else:
                # Calculate statistics for categorical columns
                value_counts = df[column].value_counts().to_dict()
                # Convert keys to strings for JSON serialization
                value_counts = {str(k): int(v) for k, v in value_counts.items()}

                summary["categorical_columns"][column] = {
                    "unique_values": int(df[column].nunique()),
                    "top_values": dict(sorted(value_counts.items(), key=lambda x: x[1], reverse=True)[:5]),
                    "missing": int(df[column].isna().sum())
                }

            # Track missing values
            missing = int(df[column].isna().sum())
            if missing > 0:
                summary["missing_values"][column] = {
                    "count": missing,
                    "percentage": float(missing / len(df) * 100)
                }

        return summary
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error summarizing dataset: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error summarizing dataset: {str(e)}",
        )


@router.get("/{data_source_id}/download")
async def download_data_source(
    data_source_id: str,
    format: str = "csv",
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
):
    """
    Download the full data for a specific data source.
    """
    try:
        # For now, we'll implement a simple mock response
        # In a real implementation, this would fetch data from storage

        # Check if the data source exists
        try:
            data_source_id_int = int(data_source_id)
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id_int).first()
        except ValueError:
            # If the ID is not an integer, try to find it by string ID
            # This is for backward compatibility with the excel_api.py implementation
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()

        if not data_source:
            # Try to find the data source in the excel_api.py DATA_SOURCES list
            # This is a temporary solution for backward compatibility
            if EXCEL_API_AVAILABLE:
                # Find the data source
                excel_data_source = None
                for source in DATA_SOURCES:
                    if source["id"] == data_source_id:
                        excel_data_source = source
                        break

                if excel_data_source:
                    # Load the data from the JSON file
                    json_path = excel_data_source["json_path"]
                    with open(json_path, "r") as f:
                        data = json.load(f)

                    # Convert to DataFrame
                    df = pd.DataFrame(data)

                    # Create a BytesIO object to store the file
                    output = BytesIO()

                    # Export based on requested format
                    if format.lower() == "excel" or format.lower() == "xlsx":
                        df.to_excel(output, index=False, engine="openpyxl")
                        media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        filename = f"{excel_data_source['name'].replace(' ', '_')}.xlsx"
                    else:  # Default to CSV
                        df.to_csv(output, index=False)
                        media_type = "text/csv"
                        filename = f"{excel_data_source['name'].replace(' ', '_')}.csv"

                    # Reset the pointer to the beginning of the BytesIO object
                    output.seek(0)

                    # Return the file as a response
                    return StreamingResponse(
                        output,
                        media_type=media_type,
                        headers={"Content-Disposition": f"attachment; filename={filename}"}
                    )

            # If we get here, the data source was not found
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Data source with ID {data_source_id} not found",
            )

        # Generate mock data for now
        # In a real implementation, this would fetch data from storage
        import random

        # Create sample data with 200 rows
        sample_data = []
        for i in range(200):
            sample_data.append({
                "id": i,
                "name": f"Item {i}",
                "value": random.randint(100, 10000) / 100,
                "date": f"2023-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}",
                "category": random.choice(["A", "B", "C", "D"]),
                "status": random.choice(["Active", "Inactive", "Pending"]),
            })

        # Convert to DataFrame
        df = pd.DataFrame(sample_data)

        # Create a BytesIO object to store the file
        output = BytesIO()

        # Export based on requested format
        if format.lower() == "excel" or format.lower() == "xlsx":
            df.to_excel(output, index=False, engine="openpyxl")
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            filename = f"data_export.xlsx"
        else:  # Default to CSV
            df.to_csv(output, index=False)
            media_type = "text/csv"
            filename = f"data_export.csv"

        # Reset the pointer to the beginning of the BytesIO object
        output.seek(0)

        # Return the file as a response
        return StreamingResponse(
            output,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading data source: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error downloading data source: {str(e)}",
        )
