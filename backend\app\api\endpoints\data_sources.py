import logging
import os
import uuid
import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.orm import Session
from io import BytesIO

from app.api.dependencies import get_current_user, get_db
from app.models.data_source import DataSource
from app.models.user import User
from app.utils.storage_factory import storage_client

# Import for Excel sheet handling
import pandas as pd
import io

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/", response_model=List[Dict[str, Any]])
async def get_data_sources(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get all data sources.
    """
    try:
        data_sources = db.query(DataSource).all()
        return [
            {
                "id": ds.id,
                "name": ds.name,
                "description": ds.description,
                "source_type": ds.source_type,
                "file_path": ds.file_path,
                "file_type": ds.file_type,
                "schema": ds.schema,
                "record_count": ds.record_count if hasattr(ds, "record_count") else 0,
                "created_at": ds.created_at.isoformat(),
                "updated_at": ds.updated_at.isoformat(),
            }
            for ds in data_sources
        ]
    except Exception as e:
        logger.error(f"Error getting data sources: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting data sources: {str(e)}",
        )


@router.get("/{data_source_id}", response_model=Dict[str, Any])
async def get_data_source(
    data_source_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get a data source by ID.
    """
    try:
        data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()
        if not data_source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Data source with ID {data_source_id} not found",
            )

        return {
            "id": data_source.id,
            "name": data_source.name,
            "description": data_source.description,
            "source_type": data_source.source_type,
            "file_path": data_source.file_path,
            "file_type": data_source.file_type,
            "schema": data_source.schema,
            "record_count": data_source.record_count if hasattr(data_source, "record_count") else 0,
            "created_at": data_source.created_at.isoformat(),
            "updated_at": data_source.updated_at.isoformat(),
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting data source: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting data source: {str(e)}",
        )


@router.post("/", response_model=Dict[str, Any])
async def create_data_source(
    data_source: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Create a new data source.
    """
    try:
        # Create data source record
        new_data_source = DataSource(
            name=data_source.get("name", "Unnamed Data Source"),
            description=data_source.get("description"),
            source_type=data_source.get("source_type", "file"),
            file_type=data_source.get("file_type"),
            schema=data_source.get("schema", {}),
            connection_details=data_source.get("connection_details"),
            file_path=data_source.get("file_path"),
            owner_id=current_user.id,
        )

        db.add(new_data_source)
        db.commit()
        db.refresh(new_data_source)

        return {
            "id": new_data_source.id,
            "name": new_data_source.name,
            "description": new_data_source.description,
            "source_type": new_data_source.source_type,
            "file_path": new_data_source.file_path,
            "file_type": new_data_source.file_type,
            "schema": new_data_source.schema,
            "record_count": 0,
            "created_at": new_data_source.created_at.isoformat(),
            "updated_at": new_data_source.updated_at.isoformat(),
        }
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating data source: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating data source: {str(e)}",
        )


@router.post("/upload", response_model=Dict[str, Any])
async def upload_file(
    file: UploadFile = File(...),
    name: str = Form(None),
    description: str = Form(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Upload a file to create a data source.
    """
    try:
        # Generate a unique file name
        file_extension = os.path.splitext(file.filename)[1] if file.filename else ""
        unique_filename = f"{uuid.uuid4()}{file_extension}"

        # Upload file to storage
        destination_path = f"uploads/{unique_filename}"
        storage_client.upload_file(file.file, destination_path)

        # Create data source record
        data_source = DataSource(
            name=name or file.filename,
            description=description,
            source_type="file",
            file_type=file_extension.lstrip("."),
            file_path=destination_path,
            schema={},
            owner_id=current_user.id,
        )

        db.add(data_source)
        db.commit()
        db.refresh(data_source)

        return {
            "id": data_source.id,
            "name": data_source.name,
            "description": data_source.description,
            "source_type": data_source.source_type,
            "file_path": data_source.file_path,
            "file_type": data_source.file_type,
            "schema": data_source.schema,
            "record_count": 0,
            "created_at": data_source.created_at.isoformat(),
            "updated_at": data_source.updated_at.isoformat(),
        }
    except Exception as e:
        db.rollback()
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error uploading file: {str(e)}",
        )


@router.post("/from-data", response_model=Dict[str, Any])
async def create_from_data(
    request: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Create a data source from parsed data.
    """
    try:
        # Extract data from request
        name = request.get("name", "Unnamed Data Source")
        description = request.get("description")
        source_type = request.get("source_type", "file")
        file_type = request.get("file_type", "csv")
        data = request.get("data", [])
        schema = request.get("schema", {})

        # Create a unique file name
        unique_filename = f"{uuid.uuid4()}.{file_type}"
        destination_path = f"uploads/{unique_filename}"

        # Create data source record
        data_source = DataSource(
            name=name,
            description=description,
            source_type=source_type,
            file_type=file_type,
            file_path=destination_path,
            schema=schema,
            owner_id=current_user.id,
        )

        # Add record count if data is provided
        if hasattr(data_source, "record_count") and data:
            data_source.record_count = len(data)

        db.add(data_source)
        db.commit()
        db.refresh(data_source)

        return {
            "id": data_source.id,
            "name": data_source.name,
            "description": data_source.description,
            "source_type": data_source.source_type,
            "file_path": data_source.file_path,
            "file_type": data_source.file_type,
            "schema": data_source.schema,
            "record_count": len(data) if data else 0,
            "created_at": data_source.created_at.isoformat(),
            "updated_at": data_source.updated_at.isoformat(),
        }
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating data source from data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating data source from data: {str(e)}",
        )


@router.post("/get-excel-sheets")
async def get_excel_sheets(
    file: UploadFile = File(...),
):
    """
    Get the sheet names from an Excel file.
    """
    try:
        # Read the Excel file
        contents = await file.read()

        # Use pandas to read the Excel file and get sheet names
        excel_file = pd.ExcelFile(io.BytesIO(contents))
        sheet_names = excel_file.sheet_names

        # Create a simple response without previews to avoid serialization issues
        return {
            "sheet_names": sheet_names,
            "default_sheet": sheet_names[0] if sheet_names else ""
        }
    except Exception as e:
        logger.error(f"Error getting Excel sheets: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting Excel sheets: {str(e)}",
        )


@router.get("/{data_source_id}/data")
async def get_data_source_data(
    data_source_id: str,
    limit: int = 100,
    full: bool = False,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get the data for a specific data source.
    """
    try:
        # For now, we'll implement a simple mock response
        # In a real implementation, this would fetch data from storage

        # Check if the data source exists
        try:
            data_source_id_int = int(data_source_id)
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id_int).first()
        except ValueError:
            # If the ID is not an integer, try to find it by string ID
            # This is for backward compatibility with the excel_api.py implementation
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()

        if not data_source:
            # Try to find the data source in the excel_api.py DATA_SOURCES list
            # This is a temporary solution for backward compatibility
            try:
                # Import the DATA_SOURCES from excel_api.py
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
                from excel_api import DATA_SOURCES

                # Find the data source
                excel_data_source = None
                for source in DATA_SOURCES:
                    if source["id"] == data_source_id:
                        excel_data_source = source
                        break

                if excel_data_source:
                    # Load the data from the JSON file
                    json_path = excel_data_source["json_path"]
                    with open(json_path, "r") as f:
                        data = json.load(f)

                    # Return full dataset if requested, otherwise limit rows
                    if full:
                        return {"data": data, "total": len(data)}
                    else:
                        return {"data": data[:limit], "total": len(data)}
            except Exception as e:
                logger.error(f"Error loading data from excel_api.py: {e}")
                pass

            # If we get here, the data source was not found
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Data source with ID {data_source_id} not found",
            )

        # Generate mock data for now
        # In a real implementation, this would fetch data from storage
        import random

        # Create sample data with 200 rows
        sample_data = []
        for i in range(200):
            sample_data.append({
                "id": i,
                "name": f"Item {i}",
                "value": random.randint(100, 10000) / 100,
                "date": f"2023-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}",
                "category": random.choice(["A", "B", "C", "D"]),
                "status": random.choice(["Active", "Inactive", "Pending"]),
            })

        # Return full dataset if requested, otherwise limit rows
        if full:
            return {"data": sample_data, "total": len(sample_data)}
        else:
            return {"data": sample_data[:limit], "total": len(sample_data)}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting data source data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting data source data: {str(e)}",
        )


@router.get("/{data_source_id}/download")
async def download_data_source(
    data_source_id: str,
    format: str = "csv",
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Download the full data for a specific data source.
    """
    try:
        # For now, we'll implement a simple mock response
        # In a real implementation, this would fetch data from storage

        # Check if the data source exists
        try:
            data_source_id_int = int(data_source_id)
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id_int).first()
        except ValueError:
            # If the ID is not an integer, try to find it by string ID
            # This is for backward compatibility with the excel_api.py implementation
            data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()

        if not data_source:
            # Try to find the data source in the excel_api.py DATA_SOURCES list
            # This is a temporary solution for backward compatibility
            try:
                # Import the DATA_SOURCES from excel_api.py
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
                from excel_api import DATA_SOURCES, StreamingResponse, BytesIO

                # Find the data source
                excel_data_source = None
                for source in DATA_SOURCES:
                    if source["id"] == data_source_id:
                        excel_data_source = source
                        break

                if excel_data_source:
                    # Load the data from the JSON file
                    json_path = excel_data_source["json_path"]
                    with open(json_path, "r") as f:
                        data = json.load(f)

                    # Convert to DataFrame
                    df = pd.DataFrame(data)

                    # Create a BytesIO object to store the file
                    output = BytesIO()

                    # Export based on requested format
                    if format.lower() == "excel" or format.lower() == "xlsx":
                        df.to_excel(output, index=False, engine="openpyxl")
                        media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        filename = f"{excel_data_source['name'].replace(' ', '_')}.xlsx"
                    else:  # Default to CSV
                        df.to_csv(output, index=False)
                        media_type = "text/csv"
                        filename = f"{excel_data_source['name'].replace(' ', '_')}.csv"

                    # Reset the pointer to the beginning of the BytesIO object
                    output.seek(0)

                    # Return the file as a response
                    return StreamingResponse(
                        output,
                        media_type=media_type,
                        headers={"Content-Disposition": f"attachment; filename={filename}"}
                    )
            except Exception as e:
                logger.error(f"Error loading data from excel_api.py: {e}")
                pass

            # If we get here, the data source was not found
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Data source with ID {data_source_id} not found",
            )

        # Generate mock data for now
        # In a real implementation, this would fetch data from storage
        import random
        from fastapi.responses import StreamingResponse
        from io import BytesIO

        # Create sample data with 200 rows
        sample_data = []
        for i in range(200):
            sample_data.append({
                "id": i,
                "name": f"Item {i}",
                "value": random.randint(100, 10000) / 100,
                "date": f"2023-{random.randint(1, 12):02d}-{random.randint(1, 28):02d}",
                "category": random.choice(["A", "B", "C", "D"]),
                "status": random.choice(["Active", "Inactive", "Pending"]),
            })

        # Convert to DataFrame
        df = pd.DataFrame(sample_data)

        # Create a BytesIO object to store the file
        output = BytesIO()

        # Export based on requested format
        if format.lower() == "excel" or format.lower() == "xlsx":
            df.to_excel(output, index=False, engine="openpyxl")
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            filename = f"data_export.xlsx"
        else:  # Default to CSV
            df.to_csv(output, index=False)
            media_type = "text/csv"
            filename = f"data_export.csv"

        # Reset the pointer to the beginning of the BytesIO object
        output.seek(0)

        # Return the file as a response
        return StreamingResponse(
            output,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading data source: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error downloading data source: {str(e)}",
        )
