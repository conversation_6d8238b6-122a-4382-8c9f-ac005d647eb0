import logging
import os
import sys
import uuid
import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.api.dependencies import get_current_user, get_db
from app.models.pipeline import Pipeline
from app.models.reconciliation import ReconciliationResult
from app.models.user import User
from app.schemas.reporting import NLQueryRequest, ReportRequest, ReportResponse
from app.services.reporting.report_generator import generate_report
from app.utils.llm_client import llm_client

# Import for backward compatibility with excel_api.py
try:
    # Add the parent directory to sys.path to import from excel_api.py
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
    from excel_api import DATA_SOURCES, PNL_REPORTS
    EXCEL_API_AVAILABLE = True
except ImportError:
    EXCEL_API_AVAILABLE = False
    DATA_SOURCES = []
    PNL_REPORTS = {}

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/generate", response_model=ReportResponse)
async def generate_report_endpoint(
    request: ReportRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Generate a report based on reconciliation results.
    """
    try:
        # Get the reconciliation result
        result = db.query(ReconciliationResult).filter(
            ReconciliationResult.id == request.reconciliation_result_id
        ).first()

        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Reconciliation result not found",
            )

        # Check if the user has access to the pipeline
        pipeline = db.query(Pipeline).filter(
            Pipeline.id == result.pipeline_id,
            Pipeline.owner_id == current_user.id
        ).first()

        if not pipeline:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this reconciliation result",
            )

        # Generate the report
        report_data = generate_report(
            result=result,
            report_type=request.report_type,
            config=request.config
        )

        return {
            "id": 1,  # This would be a database ID in a real implementation
            "name": request.name,
            "report_type": request.report_type,
            "reconciliation_result_id": request.reconciliation_result_id,
            "config": request.config,
            "data": report_data,
            "created_at": result.created_at,
            "user_id": current_user.id
        }
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating report: {str(e)}",
        )


@router.post("/query", response_model=Dict[str, Any])
async def natural_language_query(
    request: NLQueryRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Process a natural language query about reconciliation data.
    """
    try:
        # Get the reconciliation result
        result = db.query(ReconciliationResult).filter(
            ReconciliationResult.id == request.reconciliation_result_id
        ).first()

        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Reconciliation result not found",
            )

        # Check if the user has access to the pipeline
        pipeline = db.query(Pipeline).filter(
            Pipeline.id == result.pipeline_id,
            Pipeline.owner_id == current_user.id
        ).first()

        if not pipeline:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this reconciliation result",
            )

        # Process the query using LLM
        response = process_nl_query(request.query, result)

        return {
            "query": request.query,
            "response": response,
            "reconciliation_result_id": request.reconciliation_result_id
        }
    except Exception as e:
        logger.error(f"Error processing natural language query: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing query: {str(e)}",
        )


def process_nl_query(query: str, result: ReconciliationResult) -> str:
    """
    Process a natural language query using LLM.

    Args:
        query: Natural language query
        result: Reconciliation result

    Returns:
        Response to the query
    """
    try:
        # Extract relevant data from the reconciliation result
        data = {
            "total_records": result.total_records,
            "matched_records": result.matched_records,
            "anomaly_records": result.anomaly_records,
            "unmatched_records": result.unmatched_records,
            "match_rate": result.match_rate,
            "anomaly_details": result.anomaly_details,
        }

        # Create prompt for LLM
        prompt = f"""
        You are a financial data analyst assistant. Answer the following question based on the reconciliation data provided.

        Reconciliation Data:
        - Total Records: {data['total_records']}
        - Matched Records: {data['matched_records']}
        - Records with Anomalies: {data['anomaly_records']}
        - Unmatched Records: {data['unmatched_records']}
        - Match Rate: {data['match_rate'] * 100:.2f}%

        Anomaly Details:
        {data['anomaly_details']}

        Question: {query}

        Provide a concise, accurate answer based only on the data provided.
        """

        # Get response from LLM
        response = llm_client.llm.invoke(prompt)

        return response.strip()
    except Exception as e:
        logger.error(f"Error processing natural language query: {e}")
        return f"Error processing query: {str(e)}"


# Define schemas for P&L report generation
class GeneratePnLRequest(BaseModel):
    name: str
    source_ids: List[str]
    start_date: Optional[str] = None
    end_date: Optional[str] = None


@router.post("/generate/{source_id}")
async def generate_report_for_source(
    source_id: str,
    request: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Generate a simple report (P&L or 3-year forecast) from a data source.
    This endpoint is for backward compatibility with excel_api.py.
    """
    try:
        # For now, we'll use the excel_api.py implementation for report generation
        if EXCEL_API_AVAILABLE:
            # Import the generate_report function from excel_api.py
            from excel_api import generate_report as excel_generate_report

            # Call the function
            result = await excel_generate_report(source_id, request)

            return result

        # If excel_api.py is not available, implement a simple version here
        # This is a placeholder for future implementation

        # Get report type
        report_type = request.get("type", "p_and_l")

        # For now, we'll just return a mock response
        return {
            "id": str(uuid.uuid4()),
            "name": f"{report_type.upper()} Report for Source {source_id}",
            "type": report_type,
            "data_source_id": source_id,
            "created_at": datetime.now().isoformat(),
            "data": {
                "summary": {
                    "total_income": 100000,
                    "total_expenses": 75000,
                    "net_profit": 25000,
                    "profit_margin": 0.25
                },
                "details": []
            }
        }
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating report: {str(e)}",
        )


@router.post("/generate-pnl")
async def generate_pnl(
    request: GeneratePnLRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Generate a P&L report from normalized data.
    This endpoint is for backward compatibility with excel_api.py.
    """
    try:
        # For now, we'll use the excel_api.py implementation for P&L report generation
        if EXCEL_API_AVAILABLE:
            # Import the generate_pnl function from excel_api.py
            from excel_api import generate_pnl as excel_generate_pnl

            # Call the function
            result = await excel_generate_pnl(request)

            return result

        # If excel_api.py is not available, implement a simple version here
        # This is a placeholder for future implementation

        # Extract request parameters
        name = request.name
        source_ids = request.source_ids
        start_date = request.start_date
        end_date = request.end_date

        # For now, we'll just return a mock response
        report_id = str(uuid.uuid4())

        return {
            "id": report_id,
            "name": name,
            "source_ids": source_ids,
            "period_start": start_date or "2023-01-01",
            "period_end": end_date or "2023-12-31",
            "monthly_breakdown": {
                "2023-01": {"income": 10000, "expenses": 7500, "profit": 2500},
                "2023-02": {"income": 11000, "expenses": 8000, "profit": 3000},
                "2023-03": {"income": 12000, "expenses": 8500, "profit": 3500},
            },
            "category_breakdown": {
                "income": {
                    "Sales": 33000,
                    "Services": 0,
                    "Other": 0
                },
                "expenses": {
                    "Rent": 6000,
                    "Salaries": 15000,
                    "Utilities": 3000
                }
            },
            "created_at": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error generating P&L report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating P&L report: {str(e)}",
        )


@router.get("/pnl-reports")
async def get_pnl_reports(
    source_id: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get P&L reports, optionally filtered by source ID.
    This endpoint is for backward compatibility with excel_api.py.
    """
    try:
        # For now, we'll use the excel_api.py implementation for P&L reports
        if EXCEL_API_AVAILABLE:
            if source_id:
                return {k: v for k, v in PNL_REPORTS.items() if source_id in v["source_ids"]}
            return PNL_REPORTS

        # If excel_api.py is not available, implement a simple version here
        # This is a placeholder for future implementation

        # For now, we'll just return a mock response
        return {}
    except Exception as e:
        logger.error(f"Error getting P&L reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting P&L reports: {str(e)}",
        )


@router.get("/pnl-reports/{report_id}")
async def get_pnl_report(
    report_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get a specific P&L report.
    This endpoint is for backward compatibility with excel_api.py.
    """
    try:
        # For now, we'll use the excel_api.py implementation for P&L reports
        if EXCEL_API_AVAILABLE:
            if report_id in PNL_REPORTS:
                return PNL_REPORTS[report_id]

        # If not found or excel_api.py is not available, return 404
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"P&L report with ID {report_id} not found",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting P&L report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting P&L report: {str(e)}",
        )
