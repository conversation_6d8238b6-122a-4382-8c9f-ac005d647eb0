import axios from 'axios';
import { API_URL } from '@/config';

// Types
export interface CategoryCluster {
  id: string;
  label: string;
  row_count: number;
  amount_total: number;
  is_income: boolean | null;
  confidence: number;
  user_override: boolean;
  created_at: string;
  sample_rows?: any[];
}

export interface ClusterResult {
  source_id: string;
  cluster_count: number;
  clusters: CategoryCluster[];
}

export interface ClassificationResult {
  total_rows: number;
  clustered_rows: number;
  cluster_count: number;
  clusters: CategoryCluster[];
}

export interface ClassifyTransactionsRequest {
  source_id: string;
  min_cluster_size?: number;
  min_samples?: number;
  max_features?: number;
}

export interface UpdateClusterRequest {
  label?: string;
  merge_with?: string[];
  split?: boolean;
  ignore?: boolean;
}

const transactionCategorizationService = {
  /**
   * Classify transactions for a data source
   */
  classifyTransactions: async (
    request: ClassifyTransactionsRequest
  ): Promise<ClassificationResult> => {
    try {
      console.log('Classifying transactions with request:', request);
      const response = await axios.post(
        `${API_URL}/api/v1/transaction-categorization/classify-transactions`,
        request,
        {
          withCredentials: false,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      console.log('Classification response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error classifying transactions:', error);
      throw error;
    }
  },

  /**
   * Get clusters for a data source
   */
  getClusters: async (sourceId: string): Promise<ClusterResult> => {
    try {
      console.log(`Getting clusters for source ID ${sourceId}`);
      const response = await axios.get(
        `${API_URL}/api/v1/transaction-categorization/clusters/${sourceId}`,
        {
          withCredentials: false,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      console.log('Clusters response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error getting clusters:', error);
      throw error;
    }
  },

  /**
   * Update a cluster (rename, merge, split, or ignore)
   */
  updateCluster: async (
    clusterId: string,
    request: UpdateClusterRequest
  ): Promise<CategoryCluster> => {
    try {
      console.log(`Updating cluster ${clusterId} with request:`, request);
      const response = await axios.patch(
        `${API_URL}/api/v1/transaction-categorization/clusters/${clusterId}`,
        request,
        {
          withCredentials: false,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      console.log('Update response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating cluster:', error);
      throw error;
    }
  },

  /**
   * Rename a cluster
   */
  renameCluster: async (clusterId: string, newLabel: string): Promise<CategoryCluster> => {
    return transactionCategorizationService.updateCluster(clusterId, {
      label: newLabel
    });
  },

  /**
   * Merge clusters
   */
  mergeClusters: async (targetClusterId: string, clusterIds: string[]): Promise<CategoryCluster> => {
    return transactionCategorizationService.updateCluster(targetClusterId, {
      merge_with: clusterIds
    });
  }
};

export default transactionCategorizationService;
