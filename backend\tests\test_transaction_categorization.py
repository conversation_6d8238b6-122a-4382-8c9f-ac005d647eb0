import json
import os
import unittest
from unittest.mock import patch, MagicMock

import pandas as pd
import numpy as np
from fastapi.testclient import TestClient

from app.main import app
from app.services.simple_transaction_categorization import (
    categorize_transactions,
    extract_description_text,
    extract_amount_from_row,
    categorize_single_transaction,
    create_simple_category_clusters
)


class TestTransactionCategorization(unittest.TestCase):
    def setUp(self):
        self.client = TestClient(app)
        
        # Create sample transaction data
        self.sample_data = [
            {"description": "PAYMENT THANK YOU", "amount": -1000.00, "transaction_date": "2023-01-01"},
            {"description": "PAYMENT THANK YOU", "amount": -1500.00, "transaction_date": "2023-02-01"},
            {"description": "PAYMENT RECEIVED", "amount": -2000.00, "transaction_date": "2023-03-01"},
            {"description": "SALARY PAYMENT", "amount": 5000.00, "transaction_date": "2023-01-15"},
            {"description": "SALARY PAYMENT", "amount": 5000.00, "transaction_date": "2023-02-15"},
            {"description": "SALARY PAYMENT", "amount": 5000.00, "transaction_date": "2023-03-15"},
            {"description": "AMAZON.COM PAYMENT", "amount": -50.00, "transaction_date": "2023-01-05"},
            {"description": "AMAZON.COM PAYMENT", "amount": -75.00, "transaction_date": "2023-02-10"},
            {"description": "AMAZON PRIME SUBSCRIPTION", "amount": -12.99, "transaction_date": "2023-01-01"},
            {"description": "AMAZON PRIME SUBSCRIPTION", "amount": -12.99, "transaction_date": "2023-02-01"},
            {"description": "AMAZON PRIME SUBSCRIPTION", "amount": -12.99, "transaction_date": "2023-03-01"},
            {"description": "NETFLIX SUBSCRIPTION", "amount": -15.99, "transaction_date": "2023-01-05"},
            {"description": "NETFLIX SUBSCRIPTION", "amount": -15.99, "transaction_date": "2023-02-05"},
            {"description": "NETFLIX SUBSCRIPTION", "amount": -15.99, "transaction_date": "2023-03-05"},
            {"description": "GROCERY STORE PURCHASE", "amount": -120.50, "transaction_date": "2023-01-10"},
            {"description": "GROCERY STORE PURCHASE", "amount": -95.75, "transaction_date": "2023-01-20"},
            {"description": "GROCERY STORE PURCHASE", "amount": -150.25, "transaction_date": "2023-02-10"},
            {"description": "GROCERY STORE PURCHASE", "amount": -110.30, "transaction_date": "2023-02-20"},
            {"description": "GROCERY STORE PURCHASE", "amount": -130.45, "transaction_date": "2023-03-10"},
            {"description": "RESTAURANT PAYMENT", "amount": -85.00, "transaction_date": "2023-01-15"},
            {"description": "RESTAURANT PAYMENT", "amount": -65.00, "transaction_date": "2023-02-15"},
            {"description": "RESTAURANT PAYMENT", "amount": -95.00, "transaction_date": "2023-03-15"},
            {"description": "GAS STATION PURCHASE", "amount": -45.00, "transaction_date": "2023-01-05"},
            {"description": "GAS STATION PURCHASE", "amount": -50.00, "transaction_date": "2023-02-05"},
            {"description": "GAS STATION PURCHASE", "amount": -55.00, "transaction_date": "2023-03-05"},
        ]
        
        # Configuration for categorization
        self.config = {
            "min_cluster_size": 2,
            "min_samples": 1,
            "max_features": 1000
        }
        
        # Mock source ID
        self.source_id = "test-source-id"
    
    def test_extract_features(self):
        """Test feature extraction from transaction data"""
        # Convert sample data to DataFrame
        df = pd.DataFrame(self.sample_data)
        
        # Extract features
        features_df = extract_features(df, self.config)
        
        # Check that features were extracted
        self.assertIn("clean_description", features_df.columns)
        self.assertIn("combined_text", features_df.columns)
        self.assertEqual(len(features_df), len(df))
    
    def test_vectorize_features(self):
        """Test vectorization of features"""
        # Convert sample data to DataFrame
        df = pd.DataFrame(self.sample_data)
        
        # Extract features
        features_df = extract_features(df, self.config)
        
        # Vectorize features
        vectors, feature_names = vectorize_features(features_df, self.config)
        
        # Check that vectors were created
        self.assertEqual(vectors.shape[0], len(df))
        self.assertTrue(len(feature_names) > 0)
    
    def test_cluster_transactions(self):
        """Test clustering of transactions"""
        # Convert sample data to DataFrame
        df = pd.DataFrame(self.sample_data)
        
        # Extract features
        features_df = extract_features(df, self.config)
        
        # Vectorize features
        vectors, feature_names = vectorize_features(features_df, self.config)
        
        # Cluster transactions
        clusters, probabilities = cluster_transactions(vectors, self.config)
        
        # Check that clusters were created
        self.assertEqual(len(clusters), len(df))
        self.assertEqual(probabilities.shape[0], len(df))
        
        # Check that we have at least one cluster (excluding noise)
        unique_clusters = set(clusters)
        if -1 in unique_clusters:
            unique_clusters.remove(-1)
        self.assertTrue(len(unique_clusters) > 0)
    
    @patch('app.services.transaction_categorization.generate_label_with_gpt')
    def test_generate_cluster_labels(self, mock_generate_label):
        """Test generation of cluster labels"""
        # Mock the GPT label generation
        mock_generate_label.return_value = ("Test Category", 0.9)
        
        # Convert sample data to DataFrame
        df = pd.DataFrame(self.sample_data)
        
        # Extract features
        features_df = extract_features(df, self.config)
        
        # Vectorize features
        vectors, feature_names = vectorize_features(features_df, self.config)
        
        # Cluster transactions
        clusters, probabilities = cluster_transactions(vectors, self.config)
        
        # Generate cluster labels
        cluster_info = generate_cluster_labels(df, clusters, probabilities, feature_names, self.config)
        
        # Check that cluster labels were generated
        self.assertTrue(len(cluster_info) > 0)
        for info in cluster_info:
            self.assertEqual(info["label"], "Test Category")
            self.assertEqual(info["confidence"], 0.9)
    
    def test_create_category_clusters(self):
        """Test creation of category clusters"""
        # Convert sample data to DataFrame
        df = pd.DataFrame(self.sample_data)
        
        # Create sample cluster info
        cluster_info = [
            {
                "cluster_id": 0,
                "label": "Test Category 1",
                "row_count": 10,
                "amount_total": 1000.0,
                "is_income": True,
                "confidence": 0.9,
                "top_tokens": ["token1", "token2"]
            },
            {
                "cluster_id": 1,
                "label": "Test Category 2",
                "row_count": 15,
                "amount_total": -500.0,
                "is_income": False,
                "confidence": 0.8,
                "top_tokens": ["token3", "token4"]
            }
        ]
        
        # Create mock clusters
        clusters = np.zeros(len(df))
        clusters[:10] = 0
        clusters[10:] = 1
        
        # Create category clusters
        category_clusters = create_category_clusters(self.source_id, df, clusters, cluster_info)
        
        # Check that category clusters were created
        self.assertEqual(len(category_clusters), 2)
        self.assertEqual(category_clusters[0]["label"], "Test Category 1")
        self.assertEqual(category_clusters[0]["row_count"], 10)
        self.assertEqual(category_clusters[0]["amount_total"], 1000.0)
        self.assertEqual(category_clusters[0]["is_income"], True)
        self.assertEqual(category_clusters[0]["confidence"], 0.9)
        self.assertEqual(category_clusters[1]["label"], "Test Category 2")
        self.assertEqual(category_clusters[1]["row_count"], 15)
        self.assertEqual(category_clusters[1]["amount_total"], -500.0)
        self.assertEqual(category_clusters[1]["is_income"], False)
        self.assertEqual(category_clusters[1]["confidence"], 0.8)
    
    @patch('app.services.transaction_categorization.generate_label_with_gpt')
    def test_categorize_transactions(self, mock_generate_label):
        """Test the full categorization pipeline"""
        # Mock the GPT label generation
        mock_generate_label.return_value = ("Test Category", 0.9)
        
        # Categorize transactions
        result = categorize_transactions(self.source_id, self.sample_data, self.config)
        
        # Check that categorization was successful
        self.assertIn("total_rows", result)
        self.assertIn("clustered_rows", result)
        self.assertIn("cluster_count", result)
        self.assertIn("clusters", result)
        self.assertEqual(result["total_rows"], len(self.sample_data))
        self.assertTrue(result["cluster_count"] > 0)
        self.assertTrue(len(result["clusters"]) > 0)


if __name__ == "__main__":
    unittest.main()
