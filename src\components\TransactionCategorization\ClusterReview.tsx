import React, { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowUpDown, Check, Download, Edit, RefreshCw, Tag, Trash } from 'lucide-react';

import transactionCategorizationService, { CategoryCluster, ClusterResult } from '@/services/transactionCategorizationService';

interface ClusterReviewProps {
  sourceId: string;
  onComplete?: () => void;
}

const ClusterReview: React.FC<ClusterReviewProps> = ({ sourceId, onComplete }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [classifying, setClassifying] = useState(false);
  const [classificationProgress, setClassificationProgress] = useState(0);
  const [classificationStartTime, setClassificationStartTime] = useState<number | null>(null);
  const [clusterResult, setClusterResult] = useState<ClusterResult | null>(null);
  const [selectedCluster, setSelectedCluster] = useState<CategoryCluster | null>(null);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [newLabel, setNewLabel] = useState('');


  // Load clusters on mount
  useEffect(() => {
    loadClusters();
  }, [sourceId]);

  // Load clusters from the API
  const loadClusters = async () => {
    try {
      setLoading(true);
      console.log(`Loading clusters for source ID ${sourceId}`);
      const result = await transactionCategorizationService.getClusters(sourceId);
      console.log('Clusters loaded successfully:', result);
      setClusterResult(result);

      // Select the first cluster by default
      if (result.clusters.length > 0) {
        setSelectedCluster(result.clusters[0]);
      }
    } catch (error: any) {
      console.error('Error loading clusters:', error);

      // If no clusters found, show empty state
      if (error.response?.status === 404) {
        console.log('No clusters found, showing empty state');
        setClusterResult(null);
      } else {
        toast({
          title: 'Error loading clusters',
          description: error.message || 'An error occurred while loading clusters.',
          variant: 'destructive'
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Effect to update progress during classification
  useEffect(() => {
    let progressInterval: NodeJS.Timeout | null = null;

    if (classifying && classificationStartTime) {
      // Update progress every 500ms
      progressInterval = setInterval(() => {
        // Calculate elapsed time in seconds
        const elapsedSeconds = (Date.now() - classificationStartTime) / 1000;

        // Estimate progress (max 95% until complete)
        // This is just a visual indicator, not actual progress
        const estimatedProgress = Math.min(95, elapsedSeconds * 5); // 5% per second, max 95%

        setClassificationProgress(estimatedProgress);
      }, 500);
    } else {
      setClassificationProgress(0);
    }

    return () => {
      if (progressInterval) {
        clearInterval(progressInterval);
      }
    };
  }, [classifying, classificationStartTime]);

  // Classify transactions
  const handleClassifyTransactions = async () => {
    try {
      setClassifying(true);
      setClassificationStartTime(Date.now());
      setClassificationProgress(0);

      // Show toast with progress indicator
      toast({
        title: 'Classifying transactions',
        description: 'This may take a few moments...',
        duration: 60000 // Keep toast visible longer
      });

      console.log(`Classifying transactions for source ID ${sourceId}`);

      // Call API to classify transactions
      const result = await transactionCategorizationService.classifyTransactions({
        source_id: sourceId,
        min_cluster_size: 5,  // Reduced from 10 to work better with small datasets
        min_samples: 2        // Added to work better with small datasets
      });

      console.log('Classification result:', result);

      // Set progress to 100% when complete
      setClassificationProgress(100);

      // Use the classification result directly instead of reloading from database
      // (since database storage might fail but classification works)
      setClusterResult(result);

      // Select the first cluster by default
      if (result.clusters.length > 0) {
        setSelectedCluster(result.clusters[0]);
      }

      // Show success toast
      toast({
        title: 'Classification complete',
        description: `Transactions have been classified into ${result.clusters.length} categories.`,
        variant: 'default'
      });
    } catch (error: any) {
      console.error('Error classifying transactions:', error);

      let errorMessage = 'An error occurred while classifying transactions.';
      let errorDetails = '';

      // Extract more detailed error message if available
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Add more details for timeout errors
      if (error.code === 'ECONNABORTED' || errorMessage.includes('timeout')) {
        errorMessage = 'The request timed out. The transaction categorization process is taking too long.';
        errorDetails = 'Try again with a smaller dataset or different parameters.';
      }

      // Add more details for network errors
      if (error.message && error.message.includes('Network Error')) {
        errorMessage = 'Network error. Could not connect to the server.';
        errorDetails = 'Check if the backend server is running and accessible.';
      }

      toast({
        title: 'Error classifying transactions',
        description: (
          <div>
            <p>{errorMessage}</p>
            {errorDetails && <p className="mt-2 text-sm">{errorDetails}</p>}
          </div>
        ),
        variant: 'destructive'
      });
    } finally {
      setClassifying(false);
    }
  };

  // Handle cluster selection
  const handleSelectCluster = (cluster: CategoryCluster) => {
    setSelectedCluster(cluster);
  };

  // Handle rename dialog open
  const handleOpenRenameDialog = () => {
    if (selectedCluster) {
      setNewLabel(selectedCluster.label);
      setRenameDialogOpen(true);
    }
  };

  // Handle rename cluster
  const handleRenameCluster = async () => {
    if (!selectedCluster || !newLabel.trim()) return;

    try {
      // Call API to rename cluster
      const updatedCluster = await transactionCategorizationService.renameCluster(
        selectedCluster.id,
        newLabel.trim()
      );

      // Update local state
      if (clusterResult) {
        const updatedClusters = clusterResult.clusters.map(cluster =>
          cluster.id === updatedCluster.id ? updatedCluster : cluster
        );

        setClusterResult({
          ...clusterResult,
          clusters: updatedClusters
        });

        setSelectedCluster(updatedCluster);
      }

      // Close dialog
      setRenameDialogOpen(false);

      // Show success toast
      toast({
        title: 'Cluster renamed',
        description: `Cluster has been renamed to "${newLabel.trim()}".`,
        variant: 'default'
      });
    } catch (error: any) {
      toast({
        title: 'Error renaming cluster',
        description: error.message || 'An error occurred while renaming the cluster.',
        variant: 'destructive'
      });
    }
  };



  // Handle CSV download - get all transaction data and apply categories
  const handleDownloadCSV = async () => {
    if (!clusterResult || !clusterResult.clusters.length) {
      toast({
        title: 'No data to download',
        description: 'Please classify transactions first.',
        variant: 'destructive'
      });
      return;
    }

    try {
      // Show loading toast
      toast({
        title: 'Preparing download',
        description: 'Getting all transaction data...',
        duration: 5000
      });

      // Get all raw transaction data
      const response = await fetch(`http://localhost:8002/api/v1/simple/simple-data/${sourceId}?limit=10000`);
      if (!response.ok) {
        throw new Error(`Failed to fetch transaction data: ${response.statusText}`);
      }

      const { data: rawTransactions } = await response.json();

      if (!rawTransactions || rawTransactions.length === 0) {
        toast({
          title: 'No transaction data found',
          description: 'No transactions available for download.',
          variant: 'destructive'
        });
        return;
      }

      // Apply categories to all transactions using the same logic as the backend
      const csvData: any[] = [];

      rawTransactions.forEach((transaction: any) => {
        // Extract description like the backend does: f"{row['Partner Name']} {row['Booking details']}"
        const partnerName = transaction['Partner Name'] || '';
        const bookingDetails = transaction['Booking details'] || '';
        const description = `${partnerName} ${bookingDetails}`.trim();

        // Extract amount
        const amount = typeof transaction.Amount === 'number' ? transaction.Amount : parseFloat(String(transaction.Amount).replace(',', ''));

        // Apply simple categorization logic (matching the backend)
        let category = 'Uncategorized';

        // Keyword matching (same as backend KEYWORD_MAP)
        const keywords = {
          'income': 'Membership Fee Income',
          'fee': 'Membership Fee Income',
          'revenue': 'Mentoring Fee Income',
          'POS': 'Workshop and Events Expense',
          'ZOOM': 'Software Expense',
          'Gaschnitz': 'Marketing Expense',
          'Kontoführung': 'Bank Fees',
          'Bereitstellung Debitkarte': 'Bank Fees',
          'Habenzinsen': 'Bank Fees',
          'Kest': 'Bank Fees',
          'LINKEDIN': 'Marketing Expense',
          'Wildling': 'Workshop and Events Expense',
          'Cakesie': 'Workshop and Events Expense',
          'PROFESSIONAL WOMEN': 'Membership Fee Income'
        };

        // Check keywords
        for (const [keyword, cat] of Object.entries(keywords)) {
          if (description.toLowerCase().includes(keyword.toLowerCase())) {
            category = cat;
            break;
          }
        }

        // Add categorized transaction
        csvData.push({
          ...transaction,
          category: category,
          description_text: description,
          amount_parsed: amount
        });
      });

      // Convert to CSV format
      const headers = Object.keys(csvData[0]);
      const csvContent = [
        headers.join(','), // Header row
        ...csvData.map(row =>
          headers.map(header => {
            const value = row[header];
            // Escape commas and quotes in CSV
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value || '';
          }).join(',')
        )
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `transaction_categories_${sourceId}_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: 'Download successful',
        description: `Downloaded ${csvData.length} categorized transactions as CSV.`,
      });

    } catch (error) {
      console.error('Error downloading CSV:', error);
      toast({
        title: 'Download failed',
        description: 'Failed to download transaction categories.',
        variant: 'destructive'
      });
    }
  };

  // Render empty state
  if (!loading && (!clusterResult || clusterResult.cluster_count === 0)) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <div className="text-center">
          <h2 className="text-2xl font-bold">No Transaction Categories</h2>
          <p className="text-gray-500 mt-2">
            No transaction categories found for this data source. Click the button below to classify transactions.
          </p>
        </div>
        <div className="flex flex-col items-center space-y-4">
          <Button
            onClick={handleClassifyTransactions}
            disabled={classifying}
            size="lg"
          >
            {classifying ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Classifying...
              </>
            ) : (
              <>
                <Tag className="mr-2 h-4 w-4" />
                Classify Transactions
              </>
            )}
          </Button>

          {classifying && (
            <div className="w-full max-w-md">
              <div className="text-sm text-center mb-2">
                {classificationProgress < 100
                  ? `Processing... (${Math.round(classificationProgress)}%)`
                  : 'Finalizing results...'}
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-primary h-2.5 rounded-full transition-all duration-300"
                  style={{ width: `${classificationProgress}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Transaction Categories</h2>
          <p className="text-gray-500">
            {clusterResult ? `${clusterResult.cluster_count} categories found` : 'Loading categories...'}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleDownloadCSV}
            disabled={!clusterResult || clusterResult.clusters.length === 0}
          >
            <Download className="mr-2 h-4 w-4" />
            Download CSV
          </Button>
          <div className="flex flex-col">
            <Button
              onClick={handleClassifyTransactions}
              disabled={classifying}
            >
              {classifying ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Classifying...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Reclassify
                </>
              )}
            </Button>

                {classifying && (
                  <div className="mt-2 w-full">
                    <div className="text-xs text-center mb-1">
                      {Math.round(classificationProgress)}%
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className="bg-primary h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${classificationProgress}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Cluster list */}
        <div className="md:col-span-1 space-y-2">
          {loading ? (
            // Loading skeleton
            Array.from({ length: 5 }).map((_, i) => (
              <Card key={i} className="cursor-pointer">
                <CardContent className="p-4">
                  <Skeleton className="h-4 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </CardContent>
              </Card>
            ))
          ) : (
            // Cluster list
            clusterResult?.clusters.map(cluster => (
              <Card
                key={cluster.id}
                className={`cursor-pointer transition-all ${
                  selectedCluster?.id === cluster.id
                    ? 'border-primary'
                    : ''
                }`}
                onClick={() => handleSelectCluster(cluster)}
              >
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">{cluster.label}</div>
                      <div className="text-sm text-gray-500">
                        {cluster.row_count} transactions ·
                        {cluster.is_income
                          ? <span className="text-green-600"> +${Math.abs(cluster.amount_total).toFixed(2)}</span>
                          : <span className="text-red-600"> -${Math.abs(cluster.amount_total).toFixed(2)}</span>
                        }
                      </div>
                    </div>
                    <div className="flex items-center">
                      <Badge variant="outline" className="bg-blue-100 text-blue-800">
                        {cluster.confidence ? `${Math.round(cluster.confidence * 100)}%` : 'N/A'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Cluster details */}
        <div className="md:col-span-2">
          {selectedCluster ? (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{selectedCluster.label}</CardTitle>
                    <CardDescription>
                      {selectedCluster.row_count} transactions ·
                      {selectedCluster.is_income
                        ? <span className="text-green-600"> +${Math.abs(selectedCluster.amount_total).toFixed(2)}</span>
                        : <span className="text-red-600"> -${Math.abs(selectedCluster.amount_total).toFixed(2)}</span>
                      }
                    </CardDescription>
                  </div>
                  <div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleOpenRenameDialog}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Rename
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="samples">
                  <TabsList>
                    <TabsTrigger value="samples">Sample Transactions</TabsTrigger>
                    <TabsTrigger value="details">Cluster Details</TabsTrigger>
                  </TabsList>
                  <TabsContent value="samples">
                    <div className="border rounded-md">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Date</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead className="text-right">Amount</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {selectedCluster.sample_rows?.map((row, index) => (
                            <TableRow key={index}>
                              <TableCell>{row.transaction_date}</TableCell>
                              <TableCell>{row.description}</TableCell>
                              <TableCell className="text-right">
                                <span className={row.amount >= 0 ? 'text-green-600' : 'text-red-600'}>
                                  {row.amount >= 0 ? '+' : '-'}${Math.abs(row.amount).toFixed(2)}
                                </span>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </TabsContent>
                  <TabsContent value="details">
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-1">Confidence</h4>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-primary h-2.5 rounded-full"
                            style={{ width: `${selectedCluster.confidence * 100}%` }}
                          ></div>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">
                          {Math.round(selectedCluster.confidence * 100)}% confidence in this category
                        </p>
                      </div>

                      <div>
                        <h4 className="font-medium mb-1">Category Information</h4>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="bg-gray-50 p-2 rounded">
                            <span className="text-sm text-gray-500">Type:</span>
                            <div>{selectedCluster.is_income ? 'Income' : 'Expense'}</div>
                          </div>
                          <div className="bg-gray-50 p-2 rounded">
                            <span className="text-sm text-gray-500">Transaction Count:</span>
                            <div>{selectedCluster.row_count}</div>
                          </div>
                          <div className="bg-gray-50 p-2 rounded">
                            <span className="text-sm text-gray-500">Total Amount:</span>
                            <div className={selectedCluster.is_income ? 'text-green-600' : 'text-red-600'}>
                              {selectedCluster.is_income ? '+' : '-'}${Math.abs(selectedCluster.amount_total).toFixed(2)}
                            </div>
                          </div>
                          <div className="bg-gray-50 p-2 rounded">
                            <span className="text-sm text-gray-500">User Override:</span>
                            <div>{selectedCluster.user_override ? 'Yes' : 'No'}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-gray-500">
                  {loading ? 'Loading clusters...' : 'Select a category to view details'}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Rename dialog */}
      <Dialog open={renameDialogOpen} onOpenChange={setRenameDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename Category</DialogTitle>
            <DialogDescription>
              Enter a new name for this transaction category.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Input
              value={newLabel}
              onChange={(e) => setNewLabel(e.target.value)}
              placeholder="Category name"
              className="w-full"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRenameDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleRenameCluster} disabled={!newLabel.trim()}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ClusterReview;
