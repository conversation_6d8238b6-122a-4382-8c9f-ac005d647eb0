import logging
import os

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.api.router import api_router
from app.core.config import settings
from app.services.background_tasks import background_task_processor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# Set up CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router with /api/v1 prefix
app.include_router(api_router, prefix=settings.API_V1_STR)

# Include the same API router with /api prefix for backward compatibility
app.include_router(api_router, prefix="/api")

# Start background task processor if not in test mode
if os.environ.get("TESTING") != "true":
    background_task_processor.start()


@app.get("/")
async def root():
    return {
        "message": "Welcome to Data Oracle API",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    return {"status": "healthy"}