import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { useToast } from '../components/ui/use-toast';
import {
  ArrowLeft,
  RefreshCw,
  AlertTriangle,
  FileSpreadsheet,
  BarChart,
  Download,
  FileText,
  LineChart,
  Info
} from 'lucide-react';
import fileService, { DataSource, AnomalyResult, DataSummary, DataAnalysis } from '../services/excelService';
import DataSummaryView from '../components/DataSummaryView';
import DataMetadataPanel from '../components/DataMetadataPanel';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Badge } from '../components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';

const ExcelAnalysis = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [dataSource, setDataSource] = useState<DataSource | null>(null);
  const [sourceData, setSourceData] = useState<any[]>([]);
  const [anomalyResult, setAnomalyResult] = useState<AnomalyResult | null>(null);
  const [report, setReport] = useState<any | null>(null);
  const [activeTab, setActiveTab] = useState('data');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [reportType, setReportType] = useState<'p_and_l' | 'forecast'>('p_and_l');
  const [dataSummary, setDataSummary] = useState<DataSummary | null>(null);
  const [dataAnalysis, setDataAnalysis] = useState<DataAnalysis | null>(null);
  const [isSummaryLoading, setIsSummaryLoading] = useState<boolean>(false);
  const [fieldMetadata, setFieldMetadata] = useState<any[]>([]);
  const [showMetadata, setShowMetadata] = useState<boolean>(false);

  useEffect(() => {
    if (!id) {
      // If no ID is provided, redirect to the ingestion page
      console.error('No data source ID provided');
      toast({
        variant: "destructive",
        title: "Error loading data",
        description: "No data source ID was provided. Redirecting to the data ingestion page.",
      });
      setTimeout(() => {
        navigate('/excel-ingestion');
      }, 2000);
      return;
    }

    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch data source info
        const source = await fileService.getDataSource(id);
        setDataSource(source);

        // Fetch source data
        const { data } = await fileService.getDataSourceData(id, 100);
        setSourceData(data);

        // Check if there are any anomaly results for this data source
        const results = await fileService.getAnomalyResults(id);
        if (Object.keys(results).length > 0) {
          // Get the most recent result
          const resultId = Object.keys(results)[0];
          setAnomalyResult(results[resultId]);
        }

        // Fetch data summary and analysis
        fetchDataSummary();
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          variant: "destructive",
          title: "Error fetching data",
          description: "There was a problem loading the data. Please try again.",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [id, toast, navigate]);

  const fetchDataSummary = async () => {
    if (!id) {
      console.error('Cannot fetch data summary: No data source ID provided');
      return;
    }

    setIsSummaryLoading(true);
    try {
      // Fetch data summary
      const summary = await fileService.getDatasetSummary(id);
      setDataSummary(summary);

      // Fetch data analysis
      const analysis = await fileService.analyzeDataset(id);
      setDataAnalysis(analysis);

      // Generate field metadata from summary and analysis
      if (summary && sourceData.length > 0) {
        const metadata = generateFieldMetadata(summary, analysis, sourceData);
        setFieldMetadata(metadata);
        setShowMetadata(true);
      }
    } catch (error) {
      console.error('Error fetching data summary and analysis:', error);
      toast({
        variant: "destructive",
        title: "Error analyzing data",
        description: "There was a problem analyzing the data. Please try again.",
      });
    } finally {
      setIsSummaryLoading(false);
    }
  };

  // Helper function to generate field metadata
  const generateFieldMetadata = (summary: any, analysis: any, data: any[]) => {
    if (!data.length || !summary) return [];

    const allFields = Object.keys(data[0]);
    const metadata: any[] = [];

    allFields.forEach(field => {
      const fieldInfo: any = {
        name: field,
        type: 'unknown',
        sample_values: data.slice(0, 3).map(row => row[field])
      };

      // Determine field type from summary
      if (summary.numerical_columns && summary.numerical_columns[field]) {
        fieldInfo.type = 'numerical';
        fieldInfo.stats = summary.numerical_columns[field];
      } else if (summary.categorical_columns && summary.categorical_columns[field]) {
        fieldInfo.type = 'categorical';
        fieldInfo.stats = summary.categorical_columns[field];
      } else if (summary.date_columns && summary.date_columns.includes(field)) {
        fieldInfo.type = 'date';
      } else {
        // Try to infer type from data
        const sampleValue = data[0][field];
        if (typeof sampleValue === 'number') {
          fieldInfo.type = 'numerical';
        } else if (typeof sampleValue === 'string' && sampleValue.length > 20) {
          fieldInfo.type = 'text';
        } else if (typeof sampleValue === 'string') {
          fieldInfo.type = 'categorical';
        }
      }

      // Try to determine semantic type
      if (fieldInfo.type === 'date' || field.toLowerCase().includes('date') || field.toLowerCase().includes('time')) {
        fieldInfo.semantic_type = 'date';
        fieldInfo.confidence = 0.9;
      } else if (fieldInfo.type === 'numerical' &&
                (field.toLowerCase().includes('amount') ||
                 field.toLowerCase().includes('price') ||
                 field.toLowerCase().includes('cost') ||
                 field.toLowerCase().includes('value'))) {
        fieldInfo.semantic_type = 'amount';
        fieldInfo.confidence = 0.9;
      } else if (field.toLowerCase().includes('category') ||
                field.toLowerCase().includes('type') ||
                field.toLowerCase().includes('class')) {
        fieldInfo.semantic_type = 'category';
        fieldInfo.confidence = 0.8;
      } else if (field.toLowerCase().includes('desc') ||
                field.toLowerCase().includes('memo') ||
                field.toLowerCase().includes('note') ||
                field.toLowerCase().includes('comment')) {
        fieldInfo.semantic_type = 'description';
        fieldInfo.confidence = 0.8;
      } else if (field.toLowerCase().includes('account') ||
                field.toLowerCase().includes('payee') ||
                field.toLowerCase().includes('vendor') ||
                field.toLowerCase().includes('merchant')) {
        fieldInfo.semantic_type = 'account_or_payee';
        fieldInfo.confidence = 0.8;
      }

      // Add to metadata array
      metadata.push(fieldInfo);
    });

    return metadata;
  };

  const handleDetectAnomalies = async () => {
    if (!id) {
      console.error('Cannot detect anomalies: No data source ID provided');
      toast({
        variant: "destructive",
        title: "Error detecting anomalies",
        description: "No data source ID was provided.",
      });
      return;
    }

    if (!dataSource) {
      console.error('Cannot detect anomalies: No data source loaded');
      toast({
        variant: "destructive",
        title: "Error detecting anomalies",
        description: "Data source not loaded. Please try again.",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Get numerical columns
      const numericalColumns = Object.keys(sourceData[0] || {}).filter(key => {
        const value = sourceData[0][key];
        return typeof value === 'number';
      });

      // Detect anomalies
      const result = await fileService.detectAnomalies(id, numericalColumns);
      setAnomalyResult(result);

      toast({
        title: "Anomaly detection complete",
        description: `Found ${result.anomaly_count} anomalies in ${result.total_rows} records.`,
      });

      // Switch to anomalies tab
      setActiveTab('anomalies');
    } catch (error) {
      console.error('Error detecting anomalies:', error);
      toast({
        variant: "destructive",
        title: "Error detecting anomalies",
        description: "There was a problem detecting anomalies. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateReport = async () => {
    if (!id) {
      console.error('Cannot generate report: No data source ID provided');
      toast({
        variant: "destructive",
        title: "Error generating report",
        description: "No data source ID was provided.",
      });
      return;
    }

    if (!dataSource) {
      console.error('Cannot generate report: No data source loaded');
      toast({
        variant: "destructive",
        title: "Error generating report",
        description: "Data source not loaded. Please try again.",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Generate report
      const result = await fileService.generateReport(id, { type: reportType });
      setReport(result);

      toast({
        title: "Report generated",
        description: `${reportType === 'p_and_l' ? 'Profit and Loss' : '3-Year Forecast'} report generated successfully.`,
      });

      // Switch to report tab
      setActiveTab('report');
    } catch (error) {
      console.error('Error generating report:', error);
      toast({
        variant: "destructive",
        title: "Error generating report",
        description: "There was a problem generating the report. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportData = () => {
    if (!dataSource) {
      console.error('Cannot export data: No data source loaded');
      toast({
        variant: "destructive",
        title: "Error exporting data",
        description: "Data source not loaded. Please try again.",
      });
      return;
    }

    // For data tab, use the new download endpoint
    if (activeTab === 'data') {
      // Ask user for format preference
      const format = window.confirm('Download as Excel file? Click OK for Excel, Cancel for CSV.') ? 'excel' : 'csv';

      // Download the dataset
      fileService.downloadDataset(id!, format);

      toast({
        title: "Download started",
        description: `Your ${format.toUpperCase()} file will download shortly.`,
      });
      return;
    }

    // For other tabs, use the existing JSON export functionality
    let dataToExport: any;
    let filename: string;

    switch (activeTab) {
      case 'anomalies':
        if (!anomalyResult) {
          toast({
            variant: "destructive",
            title: "Error exporting anomalies",
            description: "No anomaly results available to export.",
          });
          return;
        }
        dataToExport = anomalyResult;
        filename = `${dataSource.name}-anomalies.json`;
        break;
      case 'report':
        if (!report) {
          toast({
            variant: "destructive",
            title: "Error exporting report",
            description: "No report available to export. Generate a report first.",
          });
          return;
        }
        dataToExport = report;
        filename = `${dataSource.name}-${reportType}-report.json`;
        break;
      default:
        if (!sourceData || sourceData.length === 0) {
          toast({
            variant: "destructive",
            title: "Error exporting data",
            description: "No data available to export.",
          });
          return;
        }
        dataToExport = sourceData;
        filename = `${dataSource.name}-data.json`;
    }

    // Create a JSON blob and download it
    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Export successful",
      description: `${filename} has been downloaded.`,
    });
  };

  // Helper function to render table headers from data with limited fields
  const renderTableHeaders = (data: any[]) => {
    if (!data || data.length === 0) return null;

    // Get all headers
    const allHeaders = Object.keys(data[0]);

    // Limit to important fields first, then add others up to a maximum
    const MAX_FIELDS = 8; // Maximum number of fields to display

    // Priority fields to always show if present
    const priorityFields = [
      'date', 'transaction_date', 'posting_date', 'time',
      'amount', 'value', 'balance',
      'description', 'memo', 'narrative', 'details',
      'category', 'type', 'classification',
      'account', 'payee', 'merchant'
    ];

    // Filter headers to prioritize important fields
    const priorityHeaders = allHeaders.filter(header =>
      priorityFields.some(field => header.toLowerCase().includes(field.toLowerCase()))
    );

    // Add remaining headers up to MAX_FIELDS
    const remainingHeaders = allHeaders.filter(header =>
      !priorityHeaders.includes(header)
    ).slice(0, MAX_FIELDS - priorityHeaders.length);

    // Combine priority and remaining headers
    const displayHeaders = [...priorityHeaders, ...remainingHeaders];

    // Store the selected headers for use in renderTableRows
    const selectedHeadersCount = displayHeaders.length;
    const totalHeadersCount = allHeaders.length;

    return (
      <>
        <TableHeader>
          <TableRow>
            {displayHeaders.map((header) => (
              <TableHead key={header}>{header}</TableHead>
            ))}
          </TableRow>
        </TableHeader>
        {selectedHeadersCount < totalHeadersCount && (
          <caption className="mt-2 text-sm text-gray-500 text-right">
            Showing {selectedHeadersCount} of {totalHeadersCount} fields
          </caption>
        )}
      </>
    );
  };

  // Helper function to render table rows from data with limited fields and rows
  const renderTableRows = (data: any[], anomalyIndices: number[] = []) => {
    if (!data || data.length === 0) return null;

    // Get all headers
    const allHeaders = Object.keys(data[0]);

    // Use the same logic as renderTableHeaders to get consistent fields
    const MAX_FIELDS = 8;
    const MAX_ROWS = 100; // Maximum number of rows to display

    const priorityFields = [
      'date', 'transaction_date', 'posting_date', 'time',
      'amount', 'value', 'balance',
      'description', 'memo', 'narrative', 'details',
      'category', 'type', 'classification',
      'account', 'payee', 'merchant'
    ];

    const priorityHeaders = allHeaders.filter(header =>
      priorityFields.some(field => header.toLowerCase().includes(field.toLowerCase()))
    );

    const remainingHeaders = allHeaders.filter(header =>
      !priorityHeaders.includes(header)
    ).slice(0, MAX_FIELDS - priorityHeaders.length);

    const displayHeaders = [...priorityHeaders, ...remainingHeaders];

    // Limit the number of rows displayed
    const displayData = data.slice(0, MAX_ROWS);
    const totalRows = data.length;
    const showingLimitedRows = totalRows > MAX_ROWS;

    return (
      <>
        <TableBody>
          {displayData.map((row, rowIndex) => (
            <TableRow
              key={rowIndex}
              className={anomalyIndices.includes(rowIndex) ? 'bg-red-50' : ''}
            >
              {displayHeaders.map((header, cellIndex) => {
                const value = row[header];
                return (
                  <TableCell key={cellIndex}>
                    {typeof value === 'object' ? JSON.stringify(value) : String(value || '')}
                    {anomalyIndices.includes(rowIndex) && cellIndex === 0 && (
                      <Badge variant="destructive" className="ml-2">
                        <AlertTriangle size={12} className="mr-1" />
                        Anomaly
                      </Badge>
                    )}
                  </TableCell>
                );
              })}
            </TableRow>
          ))}
        </TableBody>
        {showingLimitedRows && (
          <caption className="mt-2 text-sm text-gray-500 text-center">
            Showing {MAX_ROWS} of {totalRows} rows. Use the Export button to download the full dataset.
          </caption>
        )}
      </>
    );
  };

  // Render P&L report
  const renderPLReport = () => {
    if (!report || report.report_type !== 'p_and_l') return null;

    return (
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">{report.title}</h2>
        <p className="text-gray-500">Period: {report.period}</p>

        <Table>
          <TableBody>
            <TableRow>
              <TableCell className="font-medium">Revenue</TableCell>
              <TableCell className="text-right">${report.data.revenue.toLocaleString()}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">Cost of Goods Sold</TableCell>
              <TableCell className="text-right">${report.data.cost_of_goods_sold.toLocaleString()}</TableCell>
            </TableRow>
            <TableRow className="bg-gray-50 font-semibold">
              <TableCell>Gross Profit</TableCell>
              <TableCell className="text-right">${report.data.gross_profit.toLocaleString()}</TableCell>
            </TableRow>

            <TableRow>
              <TableCell colSpan={2} className="font-medium pt-4">Operating Expenses</TableCell>
            </TableRow>
            {Object.entries(report.data.operating_expenses).map(([key, value]: [string, any]) => (
              <TableRow key={key}>
                <TableCell className="pl-8">{key.charAt(0).toUpperCase() + key.slice(1)}</TableCell>
                <TableCell className="text-right">${value.toLocaleString()}</TableCell>
              </TableRow>
            ))}
            <TableRow className="bg-gray-50">
              <TableCell className="font-medium">Total Operating Expenses</TableCell>
              <TableCell className="text-right">${report.data.total_operating_expenses.toLocaleString()}</TableCell>
            </TableRow>

            <TableRow className="bg-gray-50 font-semibold">
              <TableCell>Operating Income</TableCell>
              <TableCell className="text-right">${report.data.operating_income.toLocaleString()}</TableCell>
            </TableRow>

            <TableRow>
              <TableCell>Other Income</TableCell>
              <TableCell className="text-right">${report.data.other_income.toLocaleString()}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Other Expenses</TableCell>
              <TableCell className="text-right">${report.data.other_expenses.toLocaleString()}</TableCell>
            </TableRow>

            <TableRow className="bg-gray-50">
              <TableCell className="font-medium">Income Before Tax</TableCell>
              <TableCell className="text-right">${report.data.income_before_tax.toLocaleString()}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Tax</TableCell>
              <TableCell className="text-right">${report.data.tax.toLocaleString()}</TableCell>
            </TableRow>

            <TableRow className="bg-blue-50 font-bold">
              <TableCell>Net Income</TableCell>
              <TableCell className="text-right">${report.data.net_income.toLocaleString()}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    );
  };

  // Render 3-year forecast report
  const renderForecastReport = () => {
    if (!report || report.report_type !== 'forecast') return null;

    return (
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">{report.title}</h2>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item</TableHead>
              {report.years.map((year: string) => (
                <TableHead key={year} className="text-right">{year}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell className="font-medium">Revenue</TableCell>
              {report.data.revenue.map((value: number, index: number) => (
                <TableCell key={index} className="text-right">${value.toLocaleString()}</TableCell>
              ))}
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">Cost of Goods Sold</TableCell>
              {report.data.cost_of_goods_sold.map((value: number, index: number) => (
                <TableCell key={index} className="text-right">${value.toLocaleString()}</TableCell>
              ))}
            </TableRow>
            <TableRow className="bg-gray-50 font-semibold">
              <TableCell>Gross Profit</TableCell>
              {report.data.gross_profit.map((value: number, index: number) => (
                <TableCell key={index} className="text-right">${value.toLocaleString()}</TableCell>
              ))}
            </TableRow>

            <TableRow>
              <TableCell colSpan={4} className="font-medium pt-4">Operating Expenses</TableCell>
            </TableRow>
            {Object.entries(report.data.operating_expenses).map(([key, values]: [string, any]) => (
              <TableRow key={key}>
                <TableCell className="pl-8">{key.charAt(0).toUpperCase() + key.slice(1)}</TableCell>
                {values.map((value: number, index: number) => (
                  <TableCell key={index} className="text-right">${value.toLocaleString()}</TableCell>
                ))}
              </TableRow>
            ))}
            <TableRow className="bg-gray-50">
              <TableCell className="font-medium">Total Operating Expenses</TableCell>
              {report.data.total_operating_expenses.map((value: number, index: number) => (
                <TableCell key={index} className="text-right">${value.toLocaleString()}</TableCell>
              ))}
            </TableRow>

            <TableRow className="bg-gray-50 font-semibold">
              <TableCell>Operating Income</TableCell>
              {report.data.operating_income.map((value: number, index: number) => (
                <TableCell key={index} className="text-right">${value.toLocaleString()}</TableCell>
              ))}
            </TableRow>

            <TableRow>
              <TableCell>Other Income</TableCell>
              {report.data.other_income.map((value: number, index: number) => (
                <TableCell key={index} className="text-right">${value.toLocaleString()}</TableCell>
              ))}
            </TableRow>
            <TableRow>
              <TableCell>Other Expenses</TableCell>
              {report.data.other_expenses.map((value: number, index: number) => (
                <TableCell key={index} className="text-right">${value.toLocaleString()}</TableCell>
              ))}
            </TableRow>

            <TableRow className="bg-gray-50">
              <TableCell className="font-medium">Income Before Tax</TableCell>
              {report.data.income_before_tax.map((value: number, index: number) => (
                <TableCell key={index} className="text-right">${value.toLocaleString()}</TableCell>
              ))}
            </TableRow>
            <TableRow>
              <TableCell>Tax</TableCell>
              {report.data.tax.map((value: number, index: number) => (
                <TableCell key={index} className="text-right">${value.toLocaleString()}</TableCell>
              ))}
            </TableRow>

            <TableRow className="bg-blue-50 font-bold">
              <TableCell>Net Income</TableCell>
              {report.data.net_income.map((value: number, index: number) => (
                <TableCell key={index} className="text-right">${value.toLocaleString()}</TableCell>
              ))}
            </TableRow>
          </TableBody>
        </Table>

        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Growth Assumptions</h3>
          <ul className="list-disc pl-5 space-y-1">
            {Object.entries(report.growth_assumptions).map(([key, value]: [string, any]) => (
              <li key={key}>
                <span className="font-medium">{key.charAt(0).toUpperCase() + key.slice(1)}:</span> {value}
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  };

  if (!dataSource || !dataSource.stats) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex justify-center items-center h-64">
          {isLoading || isSummaryLoading ? (
            <div className="flex flex-col items-center">
              <RefreshCw size={24} className="animate-spin text-gray-400 mb-2" />
              <p className="text-gray-500">Loading data...</p>
            </div>
          ) : (
            <p className="text-gray-500">Data source not found or data is incomplete</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-4"
            onClick={() => navigate('/excel-ingestion')}
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Ingestion
          </Button>
          <h1 className="text-3xl font-bold">{dataSource.name}</h1>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/financial-analysis/${id}`)}
          >
            <LineChart size={16} className="mr-2" />
            Financial Analysis
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportData}
          >
            <Download size={16} className="mr-2" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Total Rows</p>
                <p className="text-2xl font-bold">
                  {dataSource.stats.total_rows.toLocaleString()}
                </p>
              </div>
              <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                <FileSpreadsheet className="h-5 w-5 text-blue-600" />
              </div>
              {dataSource.stats.file_type && (
                <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  {dataSource.stats.file_type.toUpperCase()}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Cleaned Rows</p>
                <p className="text-2xl font-bold">
                  {dataSource.stats.cleaned_rows.toLocaleString()}
                </p>
              </div>
              <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                <FileText className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Anomalies</p>
                <p className="text-2xl font-bold">
                  {anomalyResult && anomalyResult.anomaly_count !== undefined ?
                    anomalyResult.anomaly_count.toLocaleString() : '0'}
                </p>
                <p className="text-sm text-gray-500">
                  {anomalyResult && anomalyResult.anomaly_percentage !== undefined ?
                    `${anomalyResult.anomaly_percentage.toFixed(1)}%` :
                    'Not detected yet'}
                </p>
              </div>
              <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="h-5 w-5 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Columns</p>
                <p className="text-2xl font-bold">
                  {dataSource.stats.columns.length}
                </p>
              </div>
              <div className="h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center">
                <BarChart className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="data">Data</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
          <TabsTrigger value="report">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="data" className="mt-6">
          <div className="space-y-6">
            {showMetadata && fieldMetadata.length > 0 && (
              <DataMetadataPanel
                fields={fieldMetadata}
                dataSource={dataSource}
                dataSummary={dataSummary}
              />
            )}

            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Data Preview</h2>
                  <div className="flex gap-2">
                    {!showMetadata && (
                      <Button
                        variant="outline"
                        onClick={() => setShowMetadata(true)}
                        disabled={isLoading || isSummaryLoading}
                      >
                        <Info size={16} className="mr-2" />
                        Show Metadata
                      </Button>
                    )}
                    <Button onClick={handleDetectAnomalies} disabled={isLoading}>
                      {isLoading ? (
                        <RefreshCw size={16} className="mr-2 animate-spin" />
                      ) : (
                        <AlertTriangle size={16} className="mr-2" />
                      )}
                      Detect Anomalies
                    </Button>
                  </div>
                </div>

                <div className="border rounded-md overflow-auto max-h-[500px]">
                  <Table>
                    {renderTableHeaders(sourceData)}
                    {renderTableRows(sourceData)}
                  </Table>
                </div>

                <div className="flex justify-between items-center mt-4">
                  <p className="text-sm text-gray-500">
                    Showing data from {dataSource.stats.cleaned_rows} total rows
                  </p>

                  {sourceData.length < dataSource.stats.cleaned_rows && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleExportData}
                      className="flex items-center gap-1 text-blue-600 border-blue-200 hover:bg-blue-50"
                    >
                      <Download size={14} />
                      Download Full Dataset
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="insights" className="mt-6">
          <DataSummaryView
            datasetId={id || ''}
            isLoading={isSummaryLoading}
            summary={dataSummary}
            analysis={dataAnalysis}
            onRefresh={fetchDataSummary}
          />
        </TabsContent>

        <TabsContent value="anomalies" className="mt-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Anomaly Detection Results</h2>
                <Button onClick={handleDetectAnomalies} disabled={isLoading}>
                  {isLoading ? (
                    <RefreshCw size={16} className="mr-2 animate-spin" />
                  ) : (
                    <RefreshCw size={16} className="mr-2" />
                  )}
                  Refresh
                </Button>
              </div>

              {!anomalyResult ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <AlertTriangle size={48} className="text-gray-300 mb-4" />
                  <p className="text-gray-500 mb-4">No anomalies detected yet</p>
                  <Button onClick={handleDetectAnomalies} disabled={isLoading}>
                    {isLoading ? 'Detecting...' : 'Detect Anomalies'}
                  </Button>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <Card>
                      <CardContent className="p-4">
                        <p className="text-sm text-gray-500">Anomaly Count</p>
                        <p className="text-2xl font-bold">{anomalyResult.anomaly_count}</p>
                        <p className="text-sm text-gray-500">
                          {anomalyResult.anomaly_percentage.toFixed(1)}% of total
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <p className="text-sm text-gray-500">Affected Columns</p>
                        <p className="text-2xl font-bold">
                          {Object.keys(anomalyResult.anomalies_by_column).length}
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <p className="text-sm text-gray-500">Detection Time</p>
                        <p className="text-2xl font-bold">
                          {new Date(anomalyResult.created_at).toLocaleTimeString()}
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  <h3 className="text-lg font-semibold mb-2">Anomalies by Column</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    {Object.entries(anomalyResult.anomalies_by_column).map(([column, data]: [string, any]) => (
                      <Card key={column}>
                        <CardContent className="p-4">
                          <p className="font-medium">{column}</p>
                          <p className="text-xl font-bold">{data.count} anomalies</p>
                          <p className="text-sm text-gray-500">
                            {((data.count / anomalyResult.total_rows) * 100).toFixed(1)}% of rows
                          </p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  <h3 className="text-lg font-semibold mb-2">Anomalous Records</h3>
                  <div className="border rounded-md overflow-auto max-h-[400px]">
                    <Table>
                      {renderTableHeaders(anomalyResult.anomaly_records)}
                      {renderTableRows(anomalyResult.anomaly_records)}
                    </Table>
                  </div>

                  {anomalyResult.anomaly_records.length > 100 && (
                    <div className="flex justify-end mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleExportData}
                        className="flex items-center gap-1 text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        <Download size={14} />
                        Download All Anomalies
                      </Button>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="report" className="mt-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Financial Reports</h2>
                <div className="flex items-center gap-2">
                  <Select value={reportType} onValueChange={(value: any) => setReportType(value)}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select report type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="p_and_l">Profit & Loss</SelectItem>
                      <SelectItem value="forecast">3-Year Forecast</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button onClick={handleGenerateReport} disabled={isLoading}>
                    {isLoading ? (
                      <RefreshCw size={16} className="mr-2 animate-spin" />
                    ) : (
                      <FileText size={16} className="mr-2" />
                    )}
                    Generate Report
                  </Button>
                </div>
              </div>

              {!report ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <FileText size={48} className="text-gray-300 mb-4" />
                  <p className="text-gray-500 mb-4">No reports generated yet</p>
                  <Button onClick={handleGenerateReport} disabled={isLoading}>
                    {isLoading ? 'Generating...' : 'Generate Report'}
                  </Button>
                </div>
              ) : (
                <>
                  {report.report_type === 'p_and_l' ? renderPLReport() : renderForecastReport()}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ExcelAnalysis;
