import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useToast } from '../hooks/use-toast';
import { Card, CardContent } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import {
  ArrowLeft,
  Download,
  FileText,
  LineChart,
  Tag
} from 'lucide-react';
import fileService, { DataSource } from '../services/excelService';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';

const ExcelAnalysisSimple = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [dataSource, setDataSource] = useState<DataSource | null>(null);
  const [sourceData, setSourceData] = useState<any[]>([]);
  const [report, setReport] = useState<any | null>(null);
  const [activeTab, setActiveTab] = useState('data');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [reportType, setReportType] = useState<'p_and_l' | 'forecast'>('p_and_l');

  useEffect(() => {
    const fetchData = async () => {
      if (!id) {
        console.error('No data source ID provided');
        toast({
          variant: "destructive",
          title: "Error loading data",
          description: "No data source ID was provided.",
        });
        navigate('/');
        return;
      }

      try {
        // Fetch data source info
        const source = await fileService.getDataSource(id);
        setDataSource(source);

        // Fetch source data
        const { data } = await fileService.getDataSourceData(id, 100);
        setSourceData(data);

      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          variant: "destructive",
          title: "Error loading data",
          description: "There was a problem loading the data. Please try again.",
        });
        navigate('/');
      }
    };

    fetchData();
  }, [id, toast, navigate]);

  const handleGenerateReport = async () => {
    if (!id) {
      console.error('Cannot generate report: No data source ID provided');
      toast({
        variant: "destructive",
        title: "Error generating report",
        description: "No data source ID was provided.",
      });
      return;
    }

    if (!dataSource) {
      console.error('Cannot generate report: No data source loaded');
      toast({
        variant: "destructive",
        title: "Error generating report",
        description: "Data source not loaded. Please try again.",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Generate report
      const result = await fileService.generateReport(id, { type: reportType });
      setReport(result);

      toast({
        title: "Report generated",
        description: `${reportType === 'p_and_l' ? 'Profit and Loss' : '3-Year Forecast'} report generated successfully.`,
      });

      // Switch to report tab
      setActiveTab('report');
    } catch (error) {
      console.error('Error generating report:', error);
      toast({
        variant: "destructive",
        title: "Error generating report",
        description: "There was a problem generating the report. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to render table headers from data with limited fields
  const renderTableHeaders = (data: any[]) => {
    if (!data || data.length === 0) return null;

    // Get all headers
    const allHeaders = Object.keys(data[0]);

    // Limit to important fields first, then add others up to a maximum
    const MAX_FIELDS = 8; // Maximum number of fields to display

    // Priority fields to always show if present
    const priorityFields = [
      'date', 'transaction_date', 'posting_date', 'time',
      'amount', 'value', 'balance',
      'description', 'memo', 'narrative', 'details',
      'category', 'type', 'classification',
      'account', 'payee', 'merchant'
    ];

    // Start with priority fields that exist in the data
    const displayHeaders = priorityFields.filter(field => allHeaders.includes(field));

    // Add remaining fields up to the maximum
    const remainingFields = allHeaders.filter(field => !displayHeaders.includes(field));
    const additionalFields = remainingFields.slice(0, MAX_FIELDS - displayHeaders.length);
    displayHeaders.push(...additionalFields);

    const selectedHeadersCount = displayHeaders.length;
    const totalHeadersCount = allHeaders.length;

    return (
      <>
        <TableHeader>
          <TableRow>
            {displayHeaders.map((header) => (
              <TableHead key={header}>
                {header === 'Expense Category' ? 'Category' : header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        {selectedHeadersCount < totalHeadersCount && (
          <caption className="mt-2 text-sm text-gray-500 text-right">
            Showing {selectedHeadersCount} of {totalHeadersCount} fields
          </caption>
        )}
      </>
    );
  };

  // Helper function to render table rows from data with limited fields and rows
  const renderTableRows = (data: any[]) => {
    if (!data || data.length === 0) return null;

    // Get all headers
    const allHeaders = Object.keys(data[0]);

    // Use the same logic as renderTableHeaders to get consistent fields
    const MAX_FIELDS = 8;
    const MAX_ROWS = 100; // Maximum number of rows to display

    const priorityFields = [
      'date', 'transaction_date', 'posting_date', 'time',
      'amount', 'value', 'balance',
      'description', 'memo', 'narrative', 'details',
      'category', 'type', 'classification',
      'account', 'payee', 'merchant'
    ];

    const displayHeaders = priorityFields.filter(field => allHeaders.includes(field));
    const remainingFields = allHeaders.filter(field => !displayHeaders.includes(field));
    const additionalFields = remainingFields.slice(0, MAX_FIELDS - displayHeaders.length);
    displayHeaders.push(...additionalFields);

    // Limit the number of rows displayed
    const displayData = data.slice(0, MAX_ROWS);

    return (
      <TableBody>
        {displayData.map((row, index) => (
          <TableRow key={index}>
            {displayHeaders.map((header) => (
              <TableCell key={header}>
                {row[header] !== null && row[header] !== undefined ? String(row[header]) : ''}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    );
  };

  if (!dataSource) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Loading...</h2>
          <p className="text-gray-500">Please wait while we load your data.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/')}
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Dashboard
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{dataSource.name}</h1>
            <p className="text-gray-500">
              Uploaded on {new Date(dataSource.created_at).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/financial-analysis/${id}`)}
          >
            <LineChart size={16} className="mr-2" />
            Financial Analysis
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="data">Data</TabsTrigger>
          <TabsTrigger value="report">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="data" className="mt-6">
          <div className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Transaction Data</h2>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => navigate(`/transaction-categorization/${id}`)}
                      disabled={isLoading}
                    >
                      <Tag size={16} className="mr-2" />
                      Categorize Transactions
                    </Button>
                  </div>
                </div>

                <div className="border rounded-md overflow-auto max-h-[500px]">
                  <Table>
                    {renderTableHeaders(sourceData)}
                    {renderTableRows(sourceData)}
                  </Table>
                </div>

                <div className="flex justify-between items-center mt-4">
                  <p className="text-sm text-gray-500">
                    Showing data from {dataSource.stats.cleaned_rows} total rows
                  </p>

                  {sourceData.length < dataSource.stats.cleaned_rows && (
                    <p className="text-sm text-gray-500">
                      Displaying first {sourceData.length} rows
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="report" className="mt-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Financial Reports</h2>
                <div className="flex items-center space-x-4">
                  <Select value={reportType} onValueChange={(value: 'p_and_l' | 'forecast') => setReportType(value)}>
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="p_and_l">Profit & Loss</SelectItem>
                      <SelectItem value="forecast">3-Year Forecast</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button onClick={handleGenerateReport} disabled={isLoading}>
                    {isLoading ? 'Generating...' : 'Generate Report'}
                  </Button>
                </div>
              </div>

              {!report ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <FileText size={48} className="text-gray-300 mb-4" />
                  <p className="text-gray-500 mb-4">No reports generated yet</p>
                  <Button onClick={handleGenerateReport} disabled={isLoading}>
                    {isLoading ? 'Generating...' : 'Generate Report'}
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <h2 className="text-2xl font-bold">{report.title}</h2>
                  <div className="bg-gray-50 p-4 rounded-md">
                    <pre className="text-sm">{JSON.stringify(report, null, 2)}</pre>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ExcelAnalysisSimple;
