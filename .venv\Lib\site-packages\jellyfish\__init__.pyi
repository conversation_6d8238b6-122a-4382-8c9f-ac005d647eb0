from typing import Optional

def levenshtein_distance(s1: str, s2: str) -> int: ...
def jaccard_similarity(s1: str, s2: str, ngram_size: Optional[int] = None) -> float: ...
def jaro_similarity(s1: str, s2: str) -> float: ...
def jaro_winkler_similarity(s1: str, s2: str, long_tolerance: bool = ...) -> float: ...
def damerau_levenshtein_distance(s1: str, s2: str) -> int: ...
def soundex(s: str) -> str: ...
def hamming_distance(s1: str, s2: str) -> int: ...
def nysiis(s: str) -> str: ...
def match_rating_codex(s: str) -> str: ...
def match_rating_comparison(s1: str, s2: str) -> bool: ...
def metaphone(s: str) -> str: ...
