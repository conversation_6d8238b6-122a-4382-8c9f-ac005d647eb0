import logging
import json
import re
import uuid
import os
from datetime import datetime
from typing import Any, Dict, List
from difflib import get_close_matches

import pandas as pd

# from app.models.category import CategoryCluster  # Not used in this file
from app.utils.llm_factory import get_llm_client
from app.core.config import settings

logger = logging.getLogger(__name__)

# Default categories for transaction categorization
DEFAULT_CATEGORIES = [
    "Income - Fees",
    "Income - Revenue", 
    "Income - Other",
    "Expense - Software",
    "Expense - Marketing",
    "Expense - Events",
    "Expense - Transport",
    "Expense - Bank Fees",
    "Expense - Office",
    "Expense - Other",
    "Uncategorized"
]

# Default keyword mappings (English and German)
DEFAULT_KEYWORD_MAP = {
    # English keywords
    "income": "Income - Fees",
    "fee": "Income - Fees",
    "revenue": "Income - Revenue",
    "salary": "Income - Revenue",
    "payment": "Income - Fees",
    "zoom": "Expense - Software",
    "software": "Expense - Software",
    "subscription": "Expense - Software",
    "marketing": "Expense - Marketing",
    "advertising": "Expense - Marketing",
    "event": "Expense - Events",
    "workshop": "Expense - Events",
    "conference": "Expense - Events",
    "transport": "Expense - Transport",
    "taxi": "Expense - Transport",
    "uber": "Expense - Transport",
    "bank": "Expense - Bank Fees",
    "charge": "Expense - Bank Fees",
    "office": "Expense - Office",
    "supplies": "Expense - Office",
    "equipment": "Expense - Office",

    # German keywords for Austrian bank data
    "bereitstellung": "Expense - Bank Fees",
    "debitkarte": "Expense - Bank Fees",
    "kontoführung": "Expense - Bank Fees",
    "kest": "Expense - Bank Fees",
    "habenzinsen": "Income - Other",
    "zinsen": "Income - Other",
    "gebühr": "Expense - Bank Fees",
    "spesen": "Expense - Bank Fees",
    "überweisung": "Expense - Other",
    "lastschrift": "Expense - Other",
    "dauerauftrag": "Expense - Other",
    "gutschrift": "Income - Other",
    "einzahlung": "Income - Other",
    "auszahlung": "Expense - Other",
    "bankomat": "Expense - Other",
    "kartenzahlung": "Expense - Other",

    # Business-specific keywords from the data
    "vienna.*transactions": "Income - Fees",
    "professional.*w": "Income - Fees",
    "membership": "Income - Fees",
    "mentoring": "Income - Fees",
    "linkedin": "Expense - Marketing",
    "gaschnitz": "Expense - Marketing",
    "mugs": "Expense - Marketing",
    "give.*aways": "Expense - Marketing",
    "anniversary": "Expense - Events",
    "hotel": "Expense - Events",
    "location": "Expense - Events",
    "food": "Expense - Events",
    "meetup": "Expense - Events",
    "wildling": "Expense - Events",
    "cakesie": "Expense - Events",
    "abschlussbuchung": "Uncategorized",
    "george.*transfer": "Expense - Other",
    "e.*comm": "Expense - Other",
    "impact.*hub": "Expense - Events",
    "startup.*house": "Expense - Office",
    "taxicenter": "Expense - Transport"
}

# Get the LLM client lazily when needed, not at import time
def get_llm():
    """Get the LLM client instance."""
    return get_llm_client()


def get_user_labels_file_path(source_id: str) -> str:
    """Get the file path for storing user labels for a specific source."""
    storage_dir = getattr(settings, 'STORAGE_DIR', 'storage')
    if not os.path.exists(storage_dir):
        os.makedirs(storage_dir)
    return os.path.join(storage_dir, f"user_labels_{source_id}.json")


def load_user_labels(source_id: str) -> Dict[str, str]:
    """Load user labels from file for a specific source."""
    file_path = get_user_labels_file_path(source_id)
    if os.path.exists(file_path):
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading user labels: {e}")
    return {}


def save_user_labels(source_id: str, user_labels: Dict[str, str]) -> None:
    """Save user labels to file for a specific source."""
    file_path = get_user_labels_file_path(source_id)
    try:
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(user_labels, f, ensure_ascii=False, indent=2)
        logger.info(f"Saved {len(user_labels)} user labels for source {source_id}")
    except Exception as e:
        logger.error(f"Error saving user labels: {e}")


def get_keyword_map(source_id: str) -> Dict[str, str]:
    """Get keyword map for a specific source, with defaults."""
    # For now, use default keywords. Later this could be customized per source
    # TODO: Implement per-source keyword customization using source_id
    return DEFAULT_KEYWORD_MAP.copy()


def categorize_single_transaction(
    text: str, 
    amount: float, 
    user_labels: Dict[str, str],
    keyword_map: Dict[str, str],
    categories: List[str]
) -> str:
    """
    Categorize a single transaction using the simple layered approach.
    
    Args:
        text: Transaction description text
        amount: Transaction amount
        user_labels: User-defined labels for specific descriptions
        keyword_map: Keyword to category mappings
        categories: List of available categories
        
    Returns:
        Category name
    """
    # Step 1: Check keyword mappings
    logger.debug(f"Checking keywords for text: '{text}'")
    for pattern, category in keyword_map.items():
        if re.search(pattern, text, flags=re.I):
            logger.info(f"Keyword match: '{pattern}' -> {category} for text: '{text}'")
            return category
    
    # Step 2: Check exact user labels
    if text in user_labels:
        logger.debug(f"Exact user label match: '{text}' -> {user_labels[text]}")
        return user_labels[text]
    
    # Step 3: Check fuzzy matching with user labels
    similar = get_close_matches(text, user_labels.keys(), n=1, cutoff=0.85)
    if similar:
        suggestion = user_labels[similar[0]]
        logger.debug(f"Fuzzy match: '{text}' -> '{similar[0]}' -> {suggestion}")
        return suggestion
    
    # Step 4: Use AI as fallback
    try:
        llm = get_llm()
        prompt = (
            "You are a finance assistant. Classify the transaction below.\n"
            f"Always choose exactly one of the following categories:\n"
            f"{', '.join(categories)}.\n\n"
            "Rules:\n"
            "- Negative amounts are expenses.\n"
            "- Positive amounts are income.\n"
            "- Names like 'Sara Mari' often mean reimbursements or personal transactions.\n"
            "- Names with a positive amount often mean fees.\n"
            "- Real estate, restaurants, hotels, bars, cafes or venues → 'Expense - Events'\n"
            "- Generic or unclear → 'Uncategorized'\n\n"
            f"Description: \"{text}\"\nAmount: {amount:.2f}\nCategory:"
        )

        gpt_answer = llm.generate(prompt).strip()

        # Validate that the AI response is a valid category
        if gpt_answer in categories:
            logger.info(f"AI categorization: '{text[:40]}...' ({amount:.2f}) -> {gpt_answer}")
            return gpt_answer
        else:
            logger.warning(f"AI returned invalid category '{gpt_answer}', using Uncategorized")
            return "Uncategorized"

    except Exception as e:
        logger.error(f"Error in AI categorization: {e}")
        return "Uncategorized"


def extract_description_text(df: pd.DataFrame) -> List[str]:
    """
    Extract description text from various possible columns in the DataFrame.
    
    Args:
        df: DataFrame with transaction data
        
    Returns:
        List of description texts for each row
    """
    description_texts = []
    
    # Common column names for transaction descriptions
    description_columns = [
        "description", "Description", "DESCRIPTION",
        "memo", "Memo", "MEMO",
        "narrative", "Narrative", "NARRATIVE",
        "details", "Details", "DETAILS",
        "note", "Note", "NOTE",
        "reference", "Reference", "REFERENCE",
        "booking_details", "Booking details", "Booking Details",
        "partner_name", "Partner Name", "Partner name"
    ]
    
    # Find available description columns
    available_desc_columns = [col for col in description_columns if col in df.columns]
    
    if not available_desc_columns:
        logger.warning("No description columns found, using empty strings")
        return [""] * len(df)
    
    logger.info(f"Using description columns: {available_desc_columns}")
    
    for _, row in df.iterrows():
        # Combine text from all available description columns
        text_parts = []
        for col in available_desc_columns:
            value = str(row.get(col, "")).strip()
            if value and value.lower() not in ["nan", "none", ""]:
                text_parts.append(value)

        combined_text = " ".join(text_parts)
        description_texts.append(combined_text)
    
    return description_texts


def extract_amount_from_row(row: pd.Series) -> float:
    """
    Extract amount from a transaction row.
    
    Args:
        row: Pandas Series representing a transaction row
        
    Returns:
        Transaction amount as float
    """
    # Common column names for amounts
    amount_columns = [
        "amount", "Amount", "AMOUNT",
        "value", "Value", "VALUE", 
        "transaction_amount", "Transaction_Amount",
        "sum", "Sum", "SUM",
        "total", "Total", "TOTAL"
    ]
    
    for col in amount_columns:
        if col in row.index:
            try:
                value = row[col]
                if pd.isna(value):
                    continue
                    
                # Handle string amounts with commas
                if isinstance(value, str):
                    # Remove currency symbols and spaces
                    cleaned = re.sub(r'[^\d.,-]', '', value)
                    # Replace comma with dot if it's used as decimal separator
                    if ',' in cleaned and '.' not in cleaned:
                        cleaned = cleaned.replace(',', '.')
                    elif ',' in cleaned and '.' in cleaned:
                        # European format: 1.234,56 -> 1234.56
                        if cleaned.rfind(',') > cleaned.rfind('.'):
                            cleaned = cleaned.replace('.', '').replace(',', '.')
                    value = float(cleaned)
                else:
                    value = float(value)
                    
                return value
            except (ValueError, TypeError):
                continue
    
    logger.warning(f"No valid amount found in row, using 0.0")
    return 0.0


def create_simple_category_clusters(
    source_id: str,
    categorized_transactions: List[Dict[str, Any]],
    category_counts: Dict[str, int],
    category_totals: Dict[str, float]
) -> List[Dict[str, Any]]:
    """
    Create category clusters in the expected format for the API.

    Args:
        source_id: ID of the data source
        categorized_transactions: List of categorized transactions
        category_counts: Count of transactions per category
        category_totals: Total amount per category

    Returns:
        List of category cluster dictionaries
    """
    clusters = []

    for category, count in category_counts.items():
        if count == 0:
            continue

        # Get sample transactions for this category
        category_transactions = [
            t for t in categorized_transactions
            if t.get('category') == category
        ]

        # Get sample rows (up to 5)
        sample_rows = []
        for i, transaction in enumerate(category_transactions[:5]):
            sample_row = {
                "transaction_date": transaction.get("date", transaction.get("Date", "")),
                "description": transaction.get("description_text", ""),
                "amount": extract_amount_from_row(pd.Series(transaction))
            }
            sample_rows.append(sample_row)

        # Determine if this is income or expense based on total amount
        total_amount = category_totals.get(category, 0)
        is_income = total_amount > 0 if total_amount != 0 else None

        # Create cluster
        cluster = {
            "id": str(uuid.uuid4()),
            "source_id": source_id,
            "label": category,
            "row_count": count,
            "amount_total": float(total_amount),
            "is_income": is_income,
            "confidence": 0.95 if category != "Uncategorized" else 0.5,  # High confidence for rule-based
            "rules": {
                "category_type": "simple_rule_based",
                "indices": [i for i, t in enumerate(categorized_transactions) if t.get('category') == category]
            },
            "user_override": False,
            "created_at": datetime.now().isoformat(),
            "sample_rows": sample_rows
        }
        clusters.append(cluster)

    return clusters


def categorize_transactions(
    source_id: str,
    data: List[Dict[str, Any]],
    config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Categorize transactions using the simple layered approach.

    Args:
        source_id: ID of the data source
        data: List of transaction data
        config: Configuration for categorization

    Returns:
        Dictionary with categorization results
    """
    try:
        # Validate input data
        if not data:
            logger.error("No data provided for categorization")
            raise ValueError("No data provided for categorization")

        if not isinstance(data, list):
            logger.error(f"Data must be a list, got {type(data)}")
            raise ValueError(f"Data must be a list, got {type(data)}")

        if not all(isinstance(item, dict) for item in data):
            logger.error("All items in data must be dictionaries")
            raise ValueError("All items in data must be dictionaries")

        logger.info(f"Starting simple categorization for {len(data)} transactions")

        # Load user labels and keyword mappings
        user_labels = load_user_labels(source_id)
        keyword_map = get_keyword_map(source_id)
        categories = config.get('categories', DEFAULT_CATEGORIES)

        logger.info(f"Loaded {len(user_labels)} user labels and {len(keyword_map)} keyword mappings")

        # Convert to DataFrame for easier processing
        df = pd.DataFrame(data)
        logger.info(f"Processing DataFrame with {len(df)} rows and columns: {list(df.columns)}")

        # Extract description text from various possible columns
        description_text = extract_description_text(df)

        # Categorize each transaction
        categorized_transactions = []
        uncategorized_transactions = []
        category_counts = {}
        category_totals = {}

        for i, row in df.iterrows():
            # Progress logging every 10 transactions
            if i % 10 == 0:
                logger.info(f"Processing transaction {i+1}/{len(df)}")

            # Get transaction details
            text = description_text[i] if i < len(description_text) else ""
            amount = extract_amount_from_row(row)

            # Categorize the transaction
            category = categorize_single_transaction(
                text, amount, user_labels, keyword_map, categories
            )

            # Create categorized transaction
            categorized_transaction = row.to_dict()
            categorized_transaction['category'] = category
            categorized_transaction['description_text'] = text
            categorized_transactions.append(categorized_transaction)

            # Track uncategorized for potential user review
            if category == "Uncategorized":
                uncategorized_transactions.append({
                    'index': i,
                    'text': text,
                    'amount': amount,
                    'transaction': categorized_transaction
                })

            # Update category statistics
            category_counts[category] = category_counts.get(category, 0) + 1
            category_totals[category] = category_totals.get(category, 0) + amount

        # Create category clusters in the expected format
        category_clusters = create_simple_category_clusters(
            source_id, categorized_transactions, category_counts, category_totals
        )

        # Save any new user labels that might have been learned
        save_user_labels(source_id, user_labels)

        logger.info(f"Categorization complete: {len(category_clusters)} categories, "
                   f"{len(uncategorized_transactions)} uncategorized")

        return {
            "source_id": source_id,
            "total_rows": len(data),
            "clustered_rows": len(data) - len(uncategorized_transactions),
            "unclustered_rows": len(uncategorized_transactions),
            "cluster_count": len(category_clusters),
            "clusters": category_clusters,
            "uncategorized_transactions": uncategorized_transactions[:10]  # Limit for UI
        }

    except Exception as e:
        logger.error(f"Error categorizing transactions: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise ValueError(f"Error categorizing transactions: {str(e)}")
