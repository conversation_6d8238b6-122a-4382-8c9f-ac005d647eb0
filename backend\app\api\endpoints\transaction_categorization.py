import logging
import json
import os
import sys
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.api.dependencies import get_current_user, get_db
from app.models.category import CategoryCluster
from app.models.user import User
from app.models.audit import AuditLog
from app.services.transaction_categorization import categorize_transactions

# Import for backward compatibility with excel_api.py
try:
    # Add the parent directory to sys.path to import from excel_api.py
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
    from excel_api import DATA_SOURCES, CATEGORY_CLUSTERS
    EXCEL_API_AVAILABLE = True
except ImportError:
    EXCEL_API_AVAILABLE = False
    DATA_SOURCES = []
    CATEGORY_CLUSTERS = {}

router = APIRouter()
logger = logging.getLogger(__name__)


class ClassifyTransactionsRequest(BaseModel):
    source_id: str
    min_cluster_size: int = 10
    min_samples: int = 5
    max_features: int = 10000


class UpdateClusterRequest(BaseModel):
    label: Optional[str] = None
    merge_with: Optional[List[str]] = None
    split: Optional[bool] = None
    ignore: Optional[bool] = None


def get_source_data(source_id: str) -> List[Dict[str, Any]]:
    """
    Get transaction data for a source.
    """
    try:
        # Import required modules
        from sqlalchemy.orm import Session
        from app.db.session import SessionLocal
        from app.models.data_source import DataSource
        from app.utils.storage_factory import get_storage_client
        import pandas as pd
        import numpy as np
        import os

        # Create a database session
        db = SessionLocal()

        try:
            # Try to convert to int for database query
            try:
                source_id_int = int(source_id)
                data_source = db.query(DataSource).filter(DataSource.id == source_id_int).first()
            except ValueError:
                # If not an integer, it might be a string ID
                data_source = db.query(DataSource).filter(DataSource.id == source_id).first()

            if not data_source:
                logger.error(f"Data source with ID {source_id} not found")
                raise ValueError(f"Data source with ID {source_id} not found")

            # Get storage client
            storage_client = get_storage_client()

            # Get file path
            file_path = data_source.file_path
            file_type = data_source.file_type

            # Check if the file exists in storage
            if not storage_client.file_exists(file_path):
                logger.error(f"File {file_path} not found in storage")
                raise ValueError(f"File {file_path} not found in storage")

            # Full path to the file
            full_path = os.path.join(storage_client.base_dir, file_path)
            logger.info(f"Reading file from {full_path}")

            # Check if there's a CSV version of the file (which might be easier to read)
            csv_path = os.path.splitext(full_path)[0] + ".csv"

            if os.path.exists(csv_path):
                logger.info(f"Found CSV version of the file, using it instead: {csv_path}")
                try:
                    df = pd.read_csv(csv_path, keep_default_na=False)
                except Exception as csv_error:
                    logger.error(f"Error reading CSV file: {csv_error}")
                    # Fall back to original file
                    logger.info(f"Falling back to original file: {full_path}")
                    if file_type == "csv":
                        df = pd.read_csv(full_path, keep_default_na=False)
                    elif file_type == "excel":
                        # Try multiple engines for Excel files
                        try:
                            df = pd.read_excel(full_path, engine='openpyxl', keep_default_na=False)
                        except Exception as e1:
                            logger.warning(f"Failed to read with openpyxl: {e1}")
                            try:
                                df = pd.read_excel(full_path, engine='xlrd', keep_default_na=False)
                            except Exception as e2:
                                logger.warning(f"Failed to read with xlrd: {e2}")
                                df = pd.read_excel(full_path, keep_default_na=False)
                    else:
                        raise ValueError(f"Unsupported file type: {file_type}")
            else:
                # No CSV version found, use the original file
                if file_type == "csv":
                    df = pd.read_csv(full_path, keep_default_na=False)
                elif file_type == "excel":
                    # Try multiple engines for Excel files
                    try:
                        df = pd.read_excel(full_path, engine='openpyxl', keep_default_na=False)
                    except Exception as e1:
                        logger.warning(f"Failed to read with openpyxl: {e1}")
                        try:
                            df = pd.read_excel(full_path, engine='xlrd', keep_default_na=False)
                        except Exception as e2:
                            logger.warning(f"Failed to read with xlrd: {e2}")
                            df = pd.read_excel(full_path, keep_default_na=False)
                else:
                    raise ValueError(f"Unsupported file type: {file_type}")

            # Clean up the data
            # 1. Replace NaN values with None
            df = df.where(pd.notna(df), None)

            # 2. Handle specific data types
            for col in df.columns:
                # Convert datetime columns to strings
                if pd.api.types.is_datetime64_dtype(df[col]):
                    df[col] = df[col].dt.strftime('%Y-%m-%d %H:%M:%S')
                # Handle float columns to avoid JSON serialization issues
                elif pd.api.types.is_float_dtype(df[col]):
                    df[col] = df[col].apply(lambda x: None if pd.isna(x) else float(x))
                # Handle object columns (strings)
                elif pd.api.types.is_object_dtype(df[col]):
                    # Replace empty strings with None
                    df[col] = df[col].replace('', None)
                    # Convert to string if not None
                    df[col] = df[col].apply(lambda x: str(x) if x is not None else None)

            # 3. Convert to records
            data = df.to_dict(orient='records')

            # Log success
            logger.info(f"Successfully read {len(data)} rows from {file_path}")

            return data
        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error getting source data: {e}")
        raise ValueError(f"Error getting source data: {str(e)}")


def get_sample_rows(source_id: str, indices: List[int], limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get sample rows for a cluster.
    This is a placeholder - implement based on your data source structure.
    """
    try:
        # Get all data
        data = get_source_data(source_id)

        # Filter to indices and limit
        if indices and len(indices) > 0:
            # Convert indices to integers and ensure they're valid
            valid_indices = [i for i in indices if 0 <= i < len(data)]

            # Get sample rows
            sample_indices = valid_indices[:limit]
            return [data[i] for i in sample_indices]

        return []
    except Exception as e:
        logger.error(f"Error getting sample rows: {e}")
        return []


@router.post("/classify-transactions", response_model=Dict[str, Any])
async def classify_transactions_endpoint(
    request: ClassifyTransactionsRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Classify transactions using unsupervised clustering.
    """
    try:
        # Get request parameters
        source_id = request.source_id
        min_cluster_size = request.min_cluster_size or 5  # Default to 5 if not provided
        min_samples = request.min_samples or 2  # Default to 2 if not provided
        max_features = request.max_features or 10000  # Default to 10000 if not provided

        logger.info(f"Classifying transactions for source ID {source_id} with parameters: min_cluster_size={min_cluster_size}, min_samples={min_samples}, max_features={max_features}")

        # Get data source
        try:
            data = get_source_data(source_id)
            logger.info(f"Successfully retrieved {len(data)} rows of data for source ID {source_id}")
        except Exception as data_error:
            logger.error(f"Error getting source data: {data_error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting source data: {str(data_error)}",
            )

        if not data:
            logger.warning(f"No data found for source ID {source_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Data source with ID {source_id} not found or empty",
            )

        # Check if we have enough data
        if len(data) < 5:  # Reduced from 10 to 5
            logger.warning(f"Not enough data for classification: {len(data)} rows (minimum 5 required)")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"At least 5 rows are required for classification. Found {len(data)} rows.",
            )

        # Log the number of rows for debugging
        logger.info(f"Classifying {len(data)} rows of data for source ID {source_id}")

        # Log sample data for debugging
        sample_data = data[:2]
        logger.info(f"Sample data: {sample_data}")

        # Create configuration
        config = {
            "min_cluster_size": min_cluster_size,
            "min_samples": min_samples,
            "max_features": max_features
        }

        # Delete existing clusters for this source
        try:
            existing_clusters = db.query(CategoryCluster).filter(
                CategoryCluster.source_id == source_id
            ).all()

            if existing_clusters:
                logger.info(f"Deleting {len(existing_clusters)} existing clusters for source ID {source_id}")
                for cluster in existing_clusters:
                    db.delete(cluster)
                db.commit()
        except Exception as delete_error:
            logger.error(f"Error deleting existing clusters: {delete_error}")
            # Continue with classification even if deletion fails

        # Run classification
        try:
            logger.info(f"Starting transaction categorization for source ID {source_id} with {len(data)} rows")
            # Log the first few rows of data for debugging
            if data and len(data) > 0:
                logger.info(f"First row of data: {data[0]}")
                logger.info(f"Data columns: {list(data[0].keys())}")

            result = categorize_transactions(source_id, data, config)
            logger.info(f"Classification successful. Found {len(result['clusters'])} clusters.")
        except Exception as classification_error:
            logger.error(f"Error in categorize_transactions: {classification_error}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error in transaction categorization: {str(classification_error)}",
            )

        # Store category clusters in database
        stored_clusters = []
        try:
            for cluster in result["clusters"]:
                logger.info(f"Storing cluster: {cluster['label']} with {cluster['row_count']} rows")
                category_cluster = CategoryCluster(
                    id=cluster["id"],
                    source_id=cluster["source_id"],
                    label=cluster["label"],
                    row_count=cluster["row_count"],
                    amount_total=cluster["amount_total"],
                    is_income=cluster["is_income"],
                    confidence=cluster["confidence"],
                    rules=cluster["rules"],
                    user_override=False,
                    created_by_id=current_user.id
                )
                db.add(category_cluster)
                stored_clusters.append(category_cluster)

            # Commit changes
            db.commit()
            logger.info(f"Successfully stored {len(stored_clusters)} clusters in database")
        except Exception as db_error:
            logger.error(f"Error storing clusters in database: {db_error}")
            # Rollback the transaction
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error storing clusters in database: {str(db_error)}",
            )

        # Create audit log
        try:
            audit_log = AuditLog(
                action="classify_transactions",
                category="data",
                description=f"Classified {len(data)} transactions into {len(result['clusters'])} categories",
                details={
                    "source_id": source_id,
                    "cluster_count": len(result["clusters"]),
                    "config": config
                },
                user_id=current_user.id
            )
            db.add(audit_log)
            db.commit()
            logger.info("Successfully created audit log")
        except Exception as audit_error:
            logger.error(f"Error creating audit log: {audit_error}")
            # Continue even if audit log creation fails

        # Return result
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error classifying transactions: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error classifying transactions: {str(e)}",
        )


@router.get("/clusters/{source_id}", response_model=Dict[str, Any])
async def get_clusters_endpoint(
    source_id: str,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
) -> Any:
    """
    Get clusters for a data source.
    """
    try:
        # Log the request
        logger.info(f"Getting clusters for source ID {source_id}")

        # Get clusters from database
        clusters = db.query(CategoryCluster).filter(
            CategoryCluster.source_id == source_id
        ).all()

        if not clusters:
            # Instead of raising a 404, return an empty result
            # This makes it easier for the frontend to handle
            logger.info(f"No clusters found for source ID {source_id}")
            return {
                "source_id": source_id,
                "cluster_count": 0,
                "clusters": []
            }

        # Get sample rows for each cluster
        cluster_data = []
        for cluster in clusters:
            try:
                # Get sample rows
                sample_rows = get_sample_rows(
                    source_id,
                    cluster.rules.get("indices", []) if cluster.rules else [],
                    limit=10
                )

                # Create cluster data
                cluster_data.append({
                    "id": str(cluster.id),
                    "label": cluster.label,
                    "row_count": cluster.row_count,
                    "amount_total": cluster.amount_total,
                    "is_income": cluster.is_income,
                    "confidence": cluster.confidence,
                    "user_override": cluster.user_override,
                    "created_at": cluster.created_at.isoformat(),
                    "sample_rows": sample_rows
                })
            except Exception as cluster_error:
                # Log the error but continue processing other clusters
                logger.error(f"Error processing cluster {cluster.id}: {cluster_error}")
                # Add the cluster with empty sample rows
                cluster_data.append({
                    "id": str(cluster.id),
                    "label": cluster.label,
                    "row_count": cluster.row_count,
                    "amount_total": cluster.amount_total,
                    "is_income": cluster.is_income,
                    "confidence": cluster.confidence,
                    "user_override": cluster.user_override,
                    "created_at": cluster.created_at.isoformat(),
                    "sample_rows": []
                })

        # Return clusters
        logger.info(f"Returning {len(cluster_data)} clusters for source ID {source_id}")
        return {
            "source_id": source_id,
            "cluster_count": len(clusters),
            "clusters": cluster_data
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting clusters: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting clusters: {str(e)}",
        )


@router.patch("/clusters/{cluster_id}", response_model=Dict[str, Any])
async def update_cluster_endpoint(
    cluster_id: str,
    request: UpdateClusterRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Update a cluster (rename, merge, split, or ignore).
    """
    try:
        # Get cluster from database
        cluster = db.query(CategoryCluster).filter(
            CategoryCluster.id == cluster_id
        ).first()

        if not cluster:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cluster with ID {cluster_id} not found",
            )

        # Handle different update types
        if request.label is not None:
            # Rename cluster
            old_label = cluster.label
            cluster.label = request.label
            cluster.user_override = True
            cluster.updated_by_id = current_user.id

            # Create audit log
            audit_log = AuditLog(
                action="rename_cluster",
                category="edit",
                description=f"Renamed cluster from '{old_label}' to '{request.label}'",
                details={
                    "cluster_id": cluster_id,
                    "old_label": old_label,
                    "new_label": request.label
                },
                user_id=current_user.id
            )
            db.add(audit_log)

        elif request.merge_with is not None and request.merge_with:
            # Merge clusters
            # Get clusters to merge
            clusters_to_merge = db.query(CategoryCluster).filter(
                CategoryCluster.id.in_(request.merge_with)
            ).all()

            if not clusters_to_merge:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No valid clusters to merge with",
                )

            # Merge clusters
            for merge_cluster in clusters_to_merge:
                # Update row count and amount
                cluster.row_count += merge_cluster.row_count
                cluster.amount_total += merge_cluster.amount_total

                # Merge rules
                if cluster.rules and merge_cluster.rules:
                    # Merge indices
                    if "indices" in cluster.rules and "indices" in merge_cluster.rules:
                        cluster.rules["indices"].extend(merge_cluster.rules["indices"])

                    # Merge top tokens
                    if "top_tokens" in cluster.rules and "top_tokens" in merge_cluster.rules:
                        cluster.rules["top_tokens"].extend(merge_cluster.rules["top_tokens"])
                        # Remove duplicates
                        cluster.rules["top_tokens"] = list(set(cluster.rules["top_tokens"]))

                # Create audit log for merge
                audit_log = AuditLog(
                    action="merge_cluster",
                    category="edit",
                    description=f"Merged cluster '{merge_cluster.label}' into '{cluster.label}'",
                    details={
                        "target_cluster_id": cluster_id,
                        "merged_cluster_id": str(merge_cluster.id),
                        "merged_cluster_label": merge_cluster.label
                    },
                    user_id=current_user.id
                )
                db.add(audit_log)

                # Delete merged cluster
                db.delete(merge_cluster)

            # Mark as user override
            cluster.user_override = True
            cluster.updated_by_id = current_user.id

        # Commit changes
        db.commit()

        # Return updated cluster
        return {
            "id": str(cluster.id),
            "label": cluster.label,
            "row_count": cluster.row_count,
            "amount_total": cluster.amount_total,
            "is_income": cluster.is_income,
            "confidence": cluster.confidence,
            "user_override": cluster.user_override,
            "created_at": cluster.created_at.isoformat(),
            "updated_at": cluster.updated_at.isoformat() if cluster.updated_at else None
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating cluster: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating cluster: {str(e)}",
        )
