import logging
import json
import os
import sys
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.api.dependencies import get_current_user, get_db
from app.models.category import CategoryCluster
from app.models.user import User
from app.models.audit import AuditLog
from app.services.transaction_categorization import categorize_transactions

# Import for backward compatibility with excel_api.py
try:
    # Add the parent directory to sys.path to import from excel_api.py
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
    from excel_api import DATA_SOURCES, CATEGORY_CLUSTERS
    EXCEL_API_AVAILABLE = True
except ImportError:
    EXCEL_API_AVAILABLE = False
    DATA_SOURCES = []
    CATEGORY_CLUSTERS = {}

router = APIRouter()
logger = logging.getLogger(__name__)


class ClassifyTransactionsRequest(BaseModel):
    source_id: str
    min_cluster_size: int = 10
    min_samples: int = 5
    max_features: int = 10000


class UpdateClusterRequest(BaseModel):
    label: Optional[str] = None
    merge_with: Optional[List[str]] = None
    split: Optional[bool] = None
    ignore: Optional[bool] = None


def get_source_data(source_id: str) -> List[Dict[str, Any]]:
    """
    Get transaction data for a source.
    """
    try:
        # First, try to use excel_api.py if available
        if EXCEL_API_AVAILABLE:
            # Try to find the source in DATA_SOURCES
            for source in DATA_SOURCES:
                if source["id"] == source_id:
                    json_path = source["json_path"]
                    with open(json_path, "r") as f:
                        return json.load(f)

        # If excel_api.py is not available or the source is not found,
        # try to find the source in the database
        from sqlalchemy.orm import Session
        from app.db.session import SessionLocal
        from app.models.data_source import DataSource
        from app.utils.storage_factory import get_storage_client
        import pandas as pd
        import os

        # Create a database session
        db = SessionLocal()

        try:
            # Try to convert to int for database query
            try:
                source_id_int = int(source_id)
                data_source = db.query(DataSource).filter(DataSource.id == source_id_int).first()
            except ValueError:
                # If not an integer, it might be a string ID
                data_source = db.query(DataSource).filter(DataSource.id == source_id).first()

            if data_source:
                # Get storage client
                storage_client = get_storage_client()

                # Get file path
                file_path = data_source.file_path
                file_type = data_source.file_type

                # Check if the file exists in storage
                if storage_client.file_exists(file_path):
                    # Read the file based on its type
                    full_path = os.path.join(storage_client.base_dir, file_path)

                    if file_type == "csv":
                        df = pd.read_csv(full_path)
                    elif file_type == "excel":
                        df = pd.read_excel(full_path)
                    else:
                        raise ValueError(f"Unsupported file type: {file_type}")

                    # Replace NaN, infinity, and other non-JSON-serializable values
                    df = df.fillna(None)  # Replace NaN with None
                    # Handle any other non-serializable values
                    for col in df.columns:
                        if pd.api.types.is_float_dtype(df[col]):
                            # Replace infinity values with None
                            df[col] = df[col].replace([float('inf'), float('-inf')], None)

                    # Convert to records
                    return df.to_dict(orient='records')
        finally:
            db.close()

        # If we get here, we couldn't find the data
        raise ValueError(f"Could not find data for source ID {source_id}")
    except Exception as e:
        logger.error(f"Error getting source data: {e}")
        raise ValueError(f"Error getting source data: {str(e)}")


def get_sample_rows(source_id: str, indices: List[int], limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get sample rows for a cluster.
    This is a placeholder - implement based on your data source structure.
    """
    try:
        # Get all data
        data = get_source_data(source_id)

        # Filter to indices and limit
        if indices and len(indices) > 0:
            # Convert indices to integers and ensure they're valid
            valid_indices = [i for i in indices if 0 <= i < len(data)]

            # Get sample rows
            sample_indices = valid_indices[:limit]
            return [data[i] for i in sample_indices]

        return []
    except Exception as e:
        logger.error(f"Error getting sample rows: {e}")
        return []


@router.post("/classify-transactions", response_model=Dict[str, Any])
async def classify_transactions_endpoint(
    request: ClassifyTransactionsRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Classify transactions using unsupervised clustering.
    """
    try:
        # Get request parameters
        source_id = request.source_id
        min_cluster_size = request.min_cluster_size
        min_samples = request.min_samples
        max_features = request.max_features

        # Get data source
        data = get_source_data(source_id)

        if not data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Data source with ID {source_id} not found or empty",
            )

        # Check if we have enough data
        if len(data) < 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least 100 rows are required for classification",
            )

        # Create configuration
        config = {
            "min_cluster_size": min_cluster_size,
            "min_samples": min_samples,
            "max_features": max_features
        }

        # Run classification
        result = categorize_transactions(source_id, data, config)

        # Store category clusters in database
        stored_clusters = []
        for cluster in result["clusters"]:
            category_cluster = CategoryCluster(
                id=cluster["id"],
                source_id=cluster["source_id"],
                label=cluster["label"],
                row_count=cluster["row_count"],
                amount_total=cluster["amount_total"],
                is_income=cluster["is_income"],
                confidence=cluster["confidence"],
                rules=cluster["rules"],
                user_override=False,
                created_by_id=current_user.id
            )
            db.add(category_cluster)
            stored_clusters.append(category_cluster)

        # Commit changes
        db.commit()

        # Create audit log
        audit_log = AuditLog(
            action="classify_transactions",
            category="data",
            description=f"Classified {len(data)} transactions into {len(result['clusters'])} categories",
            details={
                "source_id": source_id,
                "cluster_count": len(result["clusters"]),
                "config": config
            },
            user_id=current_user.id
        )
        db.add(audit_log)
        db.commit()

        # Return result
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error classifying transactions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error classifying transactions: {str(e)}",
        )


@router.get("/clusters/{source_id}", response_model=Dict[str, Any])
async def get_clusters_endpoint(
    source_id: str,
    db: Session = Depends(get_db),
    _: User = Depends(get_current_user),  # Keep dependency for auth but rename to _ to avoid warning
) -> Any:
    """
    Get clusters for a data source.
    """
    try:
        # Get clusters from database
        clusters = db.query(CategoryCluster).filter(
            CategoryCluster.source_id == source_id
        ).all()

        if not clusters:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No clusters found for source ID {source_id}",
            )

        # Get sample rows for each cluster
        cluster_data = []
        for cluster in clusters:
            # Get sample rows
            sample_rows = get_sample_rows(
                source_id,
                cluster.rules.get("indices", []) if cluster.rules else [],
                limit=10
            )

            # Create cluster data
            cluster_data.append({
                "id": str(cluster.id),
                "label": cluster.label,
                "row_count": cluster.row_count,
                "amount_total": cluster.amount_total,
                "is_income": cluster.is_income,
                "confidence": cluster.confidence,
                "user_override": cluster.user_override,
                "created_at": cluster.created_at.isoformat(),
                "sample_rows": sample_rows
            })

        # Return clusters
        return {
            "source_id": source_id,
            "cluster_count": len(clusters),
            "clusters": cluster_data
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting clusters: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting clusters: {str(e)}",
        )


@router.patch("/clusters/{cluster_id}", response_model=Dict[str, Any])
async def update_cluster_endpoint(
    cluster_id: str,
    request: UpdateClusterRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Update a cluster (rename, merge, split, or ignore).
    """
    try:
        # Get cluster from database
        cluster = db.query(CategoryCluster).filter(
            CategoryCluster.id == cluster_id
        ).first()

        if not cluster:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cluster with ID {cluster_id} not found",
            )

        # Handle different update types
        if request.label is not None:
            # Rename cluster
            old_label = cluster.label
            cluster.label = request.label
            cluster.user_override = True
            cluster.updated_by_id = current_user.id

            # Create audit log
            audit_log = AuditLog(
                action="rename_cluster",
                category="edit",
                description=f"Renamed cluster from '{old_label}' to '{request.label}'",
                details={
                    "cluster_id": cluster_id,
                    "old_label": old_label,
                    "new_label": request.label
                },
                user_id=current_user.id
            )
            db.add(audit_log)

        elif request.merge_with is not None and request.merge_with:
            # Merge clusters
            # Get clusters to merge
            clusters_to_merge = db.query(CategoryCluster).filter(
                CategoryCluster.id.in_(request.merge_with)
            ).all()

            if not clusters_to_merge:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No valid clusters to merge with",
                )

            # Merge clusters
            for merge_cluster in clusters_to_merge:
                # Update row count and amount
                cluster.row_count += merge_cluster.row_count
                cluster.amount_total += merge_cluster.amount_total

                # Merge rules
                if cluster.rules and merge_cluster.rules:
                    # Merge indices
                    if "indices" in cluster.rules and "indices" in merge_cluster.rules:
                        cluster.rules["indices"].extend(merge_cluster.rules["indices"])

                    # Merge top tokens
                    if "top_tokens" in cluster.rules and "top_tokens" in merge_cluster.rules:
                        cluster.rules["top_tokens"].extend(merge_cluster.rules["top_tokens"])
                        # Remove duplicates
                        cluster.rules["top_tokens"] = list(set(cluster.rules["top_tokens"]))

                # Create audit log for merge
                audit_log = AuditLog(
                    action="merge_cluster",
                    category="edit",
                    description=f"Merged cluster '{merge_cluster.label}' into '{cluster.label}'",
                    details={
                        "target_cluster_id": cluster_id,
                        "merged_cluster_id": str(merge_cluster.id),
                        "merged_cluster_label": merge_cluster.label
                    },
                    user_id=current_user.id
                )
                db.add(audit_log)

                # Delete merged cluster
                db.delete(merge_cluster)

            # Mark as user override
            cluster.user_override = True
            cluster.updated_by_id = current_user.id

        # Commit changes
        db.commit()

        # Return updated cluster
        return {
            "id": str(cluster.id),
            "label": cluster.label,
            "row_count": cluster.row_count,
            "amount_total": cluster.amount_total,
            "is_income": cluster.is_income,
            "confidence": cluster.confidence,
            "user_override": cluster.user_override,
            "created_at": cluster.created_at.isoformat(),
            "updated_at": cluster.updated_at.isoformat() if cluster.updated_at else None
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating cluster: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating cluster: {str(e)}",
        )
