import logging
import uuid
import json
import os
import sys
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.api.dependencies import get_current_user, get_db
from app.models.anomaly_detection import AnomalyDetectionJob
from app.models.data_source import DataSource
from app.models.pipeline import Pipeline
from app.models.user import User
from app.schemas.data_cleaning import CleaningRequest
from app.services.data_cleaning.anomaly_detection import detect_anomalies

# Import for backward compatibility with excel_api.py
try:
    # Add the parent directory to sys.path to import from excel_api.py
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
    from excel_api import ANOMALY_RESULTS, DATA_SOURCES
    EXCEL_API_AVAILABLE = True
except ImportError:
    EXCEL_API_AVAILABLE = False
    ANOMALY_RESULTS = {}
    DATA_SOURCES = []

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/detect", response_model=Dict[str, Any])
async def run_anomaly_detection(
    request: CleaningRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Run anomaly detection synchronously.
    """
    try:
        # Get data source
        data_source = db.query(DataSource).filter(DataSource.id == request.data_source_id).first()
        if not data_source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Data source with ID {request.data_source_id} not found",
            )

        # Get pipeline
        pipeline = db.query(Pipeline).filter(Pipeline.id == request.pipeline_id).first()
        if not pipeline:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Pipeline with ID {request.pipeline_id} not found",
            )

        # Run anomaly detection
        result = detect_anomalies(
            data_source=data_source,
            config=request.config.get("anomaly_detection", {})
        )

        return {"result": result}
    except Exception as e:
        logger.error(f"Error running anomaly detection: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error running anomaly detection: {str(e)}",
        )


@router.post("/detect-async", response_model=Dict[str, Any])
async def run_anomaly_detection_async(
    request: CleaningRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Start an asynchronous anomaly detection job.
    """
    try:
        # Get data source
        data_source = db.query(DataSource).filter(DataSource.id == request.data_source_id).first()
        if not data_source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Data source with ID {request.data_source_id} not found",
            )

        # Get pipeline
        pipeline = db.query(Pipeline).filter(Pipeline.id == request.pipeline_id).first()
        if not pipeline:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Pipeline with ID {request.pipeline_id} not found",
            )

        # Create job ID
        job_id = str(uuid.uuid4())

        # Create job record
        job = AnomalyDetectionJob(
            id=job_id,
            data_source_id=data_source.id,
            pipeline_id=pipeline.id,
            status="pending",
            progress=0.0,
            config=request.config.get("anomaly_detection", {}),
        )

        db.add(job)
        db.commit()
        db.refresh(job)

        # Start job in background task (in a real implementation, this would be a Celery task or similar)
        # For now, we'll just return the job ID

        return {
            "job_id": job_id,
            "status": "pending",
            "progress": 0.0,
            "message": "Anomaly detection job started",
            "created_at": job.created_at.isoformat(),
            "updated_at": job.updated_at.isoformat(),
        }
    except Exception as e:
        logger.error(f"Error starting anomaly detection job: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting anomaly detection job: {str(e)}",
        )


@router.get("/jobs/{job_id}/status", response_model=Dict[str, Any])
async def get_job_status(
    job_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get the status of an anomaly detection job.
    """
    try:
        job = db.query(AnomalyDetectionJob).filter(AnomalyDetectionJob.id == job_id).first()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job with ID {job_id} not found",
            )

        return {
            "job_id": job.id,
            "status": job.status,
            "progress": job.progress,
            "message": job.error_message if job.status == "failed" else None,
            "result_id": job.id if job.status == "completed" else None,
            "created_at": job.created_at.isoformat(),
            "updated_at": job.updated_at.isoformat(),
        }
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job status: {str(e)}",
        )


@router.get("/jobs/{job_id}/result", response_model=Dict[str, Any])
async def get_job_result(
    job_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get the result of a completed anomaly detection job.
    """
    try:
        job = db.query(AnomalyDetectionJob).filter(AnomalyDetectionJob.id == job_id).first()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job with ID {job_id} not found",
            )

        if job.status != "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Job with ID {job_id} is not completed (status: {job.status})",
            )

        return {"result": job.result}
    except Exception as e:
        logger.error(f"Error getting job result: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting job result: {str(e)}",
        )


@router.post("/jobs/{job_id}/cancel", response_model=Dict[str, Any])
async def cancel_job(
    job_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Cancel an anomaly detection job.
    """
    try:
        job = db.query(AnomalyDetectionJob).filter(AnomalyDetectionJob.id == job_id).first()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Job with ID {job_id} not found",
            )

        if job.status not in ["pending", "processing"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Job with ID {job_id} cannot be canceled (status: {job.status})",
            )

        # Update job status
        job.status = "failed"
        job.error_message = "Job canceled by user"
        job.updated_at = datetime.utcnow()

        db.add(job)
        db.commit()
        db.refresh(job)

        return {
            "job_id": job.id,
            "status": job.status,
            "progress": job.progress,
            "message": job.error_message,
            "created_at": job.created_at.isoformat(),
            "updated_at": job.updated_at.isoformat(),
        }
    except Exception as e:
        logger.error(f"Error canceling job: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error canceling job: {str(e)}",
        )


@router.get("/jobs", response_model=Dict[str, Any])
async def list_jobs(
    pipeline_id: Optional[int] = None,
    data_source_id: Optional[int] = None,
    status: Optional[str] = None,
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    List anomaly detection jobs with optional filters.
    """
    try:
        query = db.query(AnomalyDetectionJob)

        if pipeline_id is not None:
            query = query.filter(AnomalyDetectionJob.pipeline_id == pipeline_id)

        if data_source_id is not None:
            query = query.filter(AnomalyDetectionJob.data_source_id == data_source_id)

        if status is not None:
            query = query.filter(AnomalyDetectionJob.status == status)

        total = query.count()
        jobs = query.order_by(AnomalyDetectionJob.created_at.desc()).offset(offset).limit(limit).all()

        return {
            "total": total,
            "offset": offset,
            "limit": limit,
            "jobs": [
                {
                    "job_id": job.id,
                    "data_source_id": job.data_source_id,
                    "pipeline_id": job.pipeline_id,
                    "status": job.status,
                    "progress": job.progress,
                    "created_at": job.created_at.isoformat(),
                    "updated_at": job.updated_at.isoformat(),
                    "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                }
                for job in jobs
            ],
        }
    except Exception as e:
        logger.error(f"Error listing jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing jobs: {str(e)}",
        )


@router.get("/history/{data_source_id}", response_model=Dict[str, Any])
async def get_anomaly_detection_history(
    data_source_id: int,
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get anomaly detection history for a data source.
    """
    try:
        # Get data source
        data_source = db.query(DataSource).filter(DataSource.id == data_source_id).first()
        if not data_source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Data source with ID {data_source_id} not found",
            )

        # Get completed jobs
        query = db.query(AnomalyDetectionJob).filter(
            AnomalyDetectionJob.data_source_id == data_source_id,
            AnomalyDetectionJob.status == "completed"
        )

        total = query.count()
        jobs = query.order_by(AnomalyDetectionJob.created_at.desc()).offset(offset).limit(limit).all()

        return {
            "total": total,
            "offset": offset,
            "limit": limit,
            "jobs": [
                {
                    "job_id": job.id,
                    "data_source_id": job.data_source_id,
                    "pipeline_id": job.pipeline_id,
                    "status": job.status,
                    "config": job.config,
                    "result": job.result,
                    "created_at": job.created_at.isoformat(),
                    "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                }
                for job in jobs
            ],
        }
    except Exception as e:
        logger.error(f"Error getting anomaly detection history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting anomaly detection history: {str(e)}",
        )


@router.get("/results/{result_id}", response_model=Dict[str, Any])
async def get_anomaly_detection_result(
    result_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get anomaly detection result by ID.
    """
    try:
        # First, try to find the result in the database
        job = db.query(AnomalyDetectionJob).filter(AnomalyDetectionJob.id == result_id).first()
        if job:
            if job.status != "completed":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Job with ID {result_id} is not completed (status: {job.status})",
                )

            return {"result": job.result}

        # If not found in the database, try to find it in the excel_api.py ANOMALY_RESULTS
        if EXCEL_API_AVAILABLE and result_id in ANOMALY_RESULTS:
            return ANOMALY_RESULTS[result_id]

        # If not found anywhere, return 404
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Result with ID {result_id} not found",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting anomaly detection result: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting anomaly detection result: {str(e)}",
        )


@router.get("/results", response_model=Dict[str, Any])
async def get_anomaly_results(
    data_source_id: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get all anomaly detection results, optionally filtered by data source ID.
    """
    try:
        results = {}

        # First, get results from the database
        query = db.query(AnomalyDetectionJob).filter(AnomalyDetectionJob.status == "completed")

        if data_source_id:
            try:
                # Try to convert to int for database query
                data_source_id_int = int(data_source_id)
                query = query.filter(AnomalyDetectionJob.data_source_id == data_source_id_int)
            except ValueError:
                # If not an integer, it might be a string ID from excel_api.py
                pass

        db_jobs = query.all()

        # Add database results to the response
        for job in db_jobs:
            results[job.id] = {
                "id": job.id,
                "data_source_id": job.data_source_id,
                "pipeline_id": job.pipeline_id,
                "config": job.config,
                "result": job.result,
                "created_at": job.created_at.isoformat(),
                "completed_at": job.completed_at.isoformat() if job.completed_at else None,
            }

        # Then, if excel_api.py is available, get results from there
        if EXCEL_API_AVAILABLE:
            for result_id, result in ANOMALY_RESULTS.items():
                if data_source_id:
                    # Filter by data source ID if provided
                    if result.get("data_source_id") == data_source_id:
                        results[result_id] = result
                else:
                    results[result_id] = result

        return results
    except Exception as e:
        logger.error(f"Error getting anomaly results: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting anomaly results: {str(e)}",
        )


@router.post("/detect/{source_id}", response_model=Dict[str, Any])
async def detect_anomalies_endpoint(
    source_id: str,
    request: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Detect anomalies in a data source.
    This endpoint is for backward compatibility with excel_api.py.
    """
    try:
        # First, try to find the data source in the database
        try:
            # Try to convert to int for database query
            source_id_int = int(source_id)
            data_source = db.query(DataSource).filter(DataSource.id == source_id_int).first()
        except ValueError:
            # If not an integer, it might be a string ID from excel_api.py
            data_source = None

        if data_source:
            # If found in the database, use the structured app implementation
            # Create a pipeline for this operation
            pipeline = Pipeline(
                name=f"Anomaly Detection for {data_source.name}",
                description="Automatically created pipeline for anomaly detection",
                owner_id=current_user.id,
            )

            db.add(pipeline)
            db.commit()
            db.refresh(pipeline)

            # Create a cleaning request
            cleaning_request = CleaningRequest(
                data_source_id=data_source.id,
                pipeline_id=pipeline.id,
                operations=["anomaly_detection"],
                config={
                    "anomaly_detection": {
                        "columns": request.get("columns"),
                        "threshold": request.get("threshold", 3.0),
                        "methods": ["statistical"],
                        "statistical_method": "z_score",
                        "save_output": True
                    }
                }
            )

            # Run anomaly detection
            result = await run_anomaly_detection(cleaning_request, db, current_user)

            # Create a result ID
            result_id = str(uuid.uuid4())

            # Store the result
            job = AnomalyDetectionJob(
                id=result_id,
                data_source_id=data_source.id,
                pipeline_id=pipeline.id,
                status="completed",
                progress=100.0,
                config=cleaning_request.config.get("anomaly_detection", {}),
                result=result.get("result", {}),
                completed_at=datetime.utcnow(),
            )

            db.add(job)
            db.commit()
            db.refresh(job)

            # Return the result
            return {
                "id": result_id,
                "data_source_id": source_id,
                "anomalies": result.get("result", {}).get("anomalies", []),
                "stats": result.get("result", {}).get("stats", {}),
                "created_at": job.created_at.isoformat(),
            }

        # If not found in the database, try to use excel_api.py
        if EXCEL_API_AVAILABLE:
            # Find the data source in excel_api.py
            data_source = None
            for source in DATA_SOURCES:
                if source["id"] == source_id:
                    data_source = source
                    break

            if not data_source:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Data source with ID {source_id} not found",
                )

            # Load the data
            with open(data_source["json_path"], "r") as f:
                data = json.load(f)

            # Get columns to analyze
            columns = request.get("columns")
            if not columns:
                # If no columns specified, use all numeric columns
                import pandas as pd
                df = pd.DataFrame(data)
                columns = df.select_dtypes(include=["number"]).columns.tolist()

            # Get threshold
            threshold = request.get("threshold", 3.0)

            # Detect anomalies
            import pandas as pd
            import numpy as np
            from scipy import stats

            df = pd.DataFrame(data)
            anomalies = []

            for column in columns:
                if column in df.columns and pd.api.types.is_numeric_dtype(df[column]):
                    # Calculate z-scores
                    z_scores = np.abs(stats.zscore(df[column].fillna(df[column].mean())))

                    # Find anomalies
                    anomaly_indices = np.where(z_scores > threshold)[0]

                    for idx in anomaly_indices:
                        anomalies.append({
                            "row_index": int(idx),
                            "column": column,
                            "value": float(df.iloc[idx][column]),
                            "z_score": float(z_scores[idx]),
                            "threshold": float(threshold)
                        })

            # Create a result ID
            result_id = str(uuid.uuid4())

            # Create the result
            result = {
                "id": result_id,
                "data_source_id": source_id,
                "anomalies": anomalies,
                "stats": {
                    "total_rows": len(df),
                    "total_anomalies": len(anomalies),
                    "columns_analyzed": columns,
                    "threshold": threshold
                },
                "created_at": datetime.now().isoformat()
            }

            # Store the result
            ANOMALY_RESULTS[result_id] = result

            return result

        # If neither database nor excel_api.py is available, return 404
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Data source with ID {source_id} not found",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error detecting anomalies: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error detecting anomalies: {str(e)}",
        )


@router.post("/detect-combined/{combined_id}", response_model=Dict[str, Any])
async def detect_anomalies_combined(
    combined_id: str,
    request: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Detect anomalies in a combined dataset.
    This endpoint is for backward compatibility with excel_api.py.
    """
    try:
        # For now, we'll only support excel_api.py combined datasets
        if EXCEL_API_AVAILABLE:
            # Import COMBINED_DATASETS from excel_api.py
            from excel_api import COMBINED_DATASETS

            if combined_id not in COMBINED_DATASETS:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Combined dataset with ID {combined_id} not found",
                )

            combined_dataset = COMBINED_DATASETS[combined_id]

            # Load the data
            with open(combined_dataset["json_path"], "r") as f:
                data = json.load(f)

            # Get columns to analyze
            columns = request.get("columns")
            if not columns:
                # If no columns specified, use all numeric columns
                import pandas as pd
                df = pd.DataFrame(data)
                columns = df.select_dtypes(include=["number"]).columns.tolist()

            # Get threshold
            threshold = request.get("threshold", 3.0)

            # Detect anomalies
            import pandas as pd
            import numpy as np
            from scipy import stats

            df = pd.DataFrame(data)
            anomalies = []

            for column in columns:
                if column in df.columns and pd.api.types.is_numeric_dtype(df[column]):
                    # Calculate z-scores
                    z_scores = np.abs(stats.zscore(df[column].fillna(df[column].mean())))

                    # Find anomalies
                    anomaly_indices = np.where(z_scores > threshold)[0]

                    for idx in anomaly_indices:
                        anomalies.append({
                            "row_index": int(idx),
                            "column": column,
                            "value": float(df.iloc[idx][column]),
                            "z_score": float(z_scores[idx]),
                            "threshold": float(threshold)
                        })

            # Create a result ID
            result_id = str(uuid.uuid4())

            # Create the result
            result = {
                "id": result_id,
                "combined_id": combined_id,
                "anomalies": anomalies,
                "stats": {
                    "total_rows": len(df),
                    "total_anomalies": len(anomalies),
                    "columns_analyzed": columns,
                    "threshold": threshold
                },
                "created_at": datetime.now().isoformat()
            }

            # Store the result
            ANOMALY_RESULTS[result_id] = result

            return result

        # If excel_api.py is not available, return 404
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Combined dataset with ID {combined_id} not found",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error detecting anomalies in combined dataset: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error detecting anomalies in combined dataset: {str(e)}",
        )
