@echo off
echo ===================================================
echo Financial Data Analysis System - Setup and Run
echo ===================================================
echo.

echo Step 1: Installing Python dependencies...
echo ---------------------------------------------------
cd backend
pip install -r requirements.txt
pip install pandas numpy scikit-learn hdbscan openpyxl tenacity
cd ..
echo.

echo Step 2: Starting the backend server...
echo ---------------------------------------------------
start cmd /k "cd backend && python run_consolidated_api.py"
echo.

echo Step 3: Starting the frontend development server...
echo ---------------------------------------------------
start cmd /k "cd frontend && npm install && npm run dev"
echo.

echo ===================================================
echo Setup complete! The application should now be running.
echo.
echo - Backend: http://localhost:8002
echo - Frontend: http://localhost:4001
echo - API Documentation: http://localhost:8002/docs
echo.
echo If you encounter any issues:
echo 1. Make sure all required Python packages are installed
echo 2. Check that both servers are running
echo 3. Verify that the frontend can connect to the backend
echo ===================================================
