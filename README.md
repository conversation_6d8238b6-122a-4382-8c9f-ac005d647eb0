# Data Oracle Predict

A flexible data ingestion, cleaning, and reconciliation platform with LLM integration and reporting capabilities.

## Project Overview

Data Oracle Predict is a comprehensive platform designed for financial institutions to ingest, clean, reconcile, and analyze financial data. The platform leverages AI and machine learning to automate data processing and provide valuable insights.

### Key Features

- **Data Ingestion**: Upload files, connect to databases, or integrate with cloud services
- **Data Cleaning**: Standardize data, detect anomalies, and classify data using LLMs
- **Transaction Categorization**: Automatically categorize transactions using AI-powered clustering
- **Reconciliation**: Match data from different sources with exact, fuzzy, and semantic matching
- **Reporting**: Generate customized reports and visualizations
- **Natural Language Queries**: Ask questions about your data in plain English
- **Audit Trail**: Track all changes and actions for compliance

## Architecture

The project consists of:

1. **Frontend**: React application with TypeScript and Tailwind CSS
2. **Backend**: FastAPI application with SQLAlchemy and Pydantic
3. **Database**: PostgreSQL for relational data storage
4. **Cloud Integration**: Google Cloud Platform (GCS, BigQuery, Vertex AI)

## Getting Started

### Prerequisites

- Python 3.11+
- Node.js 18+
- Docker and Docker Compose
- Google Cloud Platform account with the following services enabled:
  - Cloud Storage
  - BigQuery
  - Vertex AI

### Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/data-oracle-predict.git
   cd data-oracle-predict
   ```

2. Create a `.env` file based on `.env.example`:
   ```bash
   cp .env.example .env
   ```

3. Update the `.env` file with your GCP credentials and other configuration.

4. Start the application using Docker Compose:
   ```bash
   docker-compose up -d
   ```

5. Access the application:
   - Frontend: http://localhost:8080
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Development Setup

#### Frontend

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

#### Backend

1. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

3. Start the FastAPI server:
   ```bash
   uvicorn app.main:app --reload
   ```

## Deployment

### GCP Deployment

1. Build the Docker images:
   ```bash
   docker build -t gcr.io/your-project-id/data-oracle-frontend .
   docker build -t gcr.io/your-project-id/data-oracle-backend ./backend
   ```

2. Push the images to Google Container Registry:
   ```bash
   docker push gcr.io/your-project-id/data-oracle-frontend
   docker push gcr.io/your-project-id/data-oracle-backend
   ```

3. Deploy to Google Cloud Run or GKE using the provided Terraform scripts.

## Technologies Used

### Frontend
- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- React Router
- React Query

### Backend
- FastAPI
- SQLAlchemy
- Pydantic
- Pandas
- LangChain
- Google Cloud Libraries

## License

This project is licensed under the MIT License - see the LICENSE file for details.
