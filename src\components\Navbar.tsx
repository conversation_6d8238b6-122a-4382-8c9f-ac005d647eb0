
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Menu, X } from 'lucide-react';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className="fixed top-0 w-full bg-white/80 backdrop-blur-md z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <span className="text-2xl font-bold text-navy">
                Data<span className="text-teal">Oracle</span>
              </span>
            </Link>
          </div>

          {/* Desktop navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <Link to="/" className="text-gray-600 hover:text-navy transition-colors">
              Data Analyzer
            </Link>
            <a href="https://github.com/corinall/data-oracle-predict" target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-navy transition-colors">
              GitHub
            </a>
            <Button className="bg-navy hover:bg-navy/90 text-white">
              Documentation
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button onClick={toggleMenu} className="p-2">
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="bg-white px-2 pt-2 pb-3 space-y-1 sm:px-3">
              <Link
                to="/"
                className="block px-3 py-2 text-gray-600 hover:text-navy"
                onClick={toggleMenu}
              >
                Data Analyzer
              </Link>
              <a
                href="https://github.com/corinall/data-oracle-predict"
                target="_blank"
                rel="noopener noreferrer"
                className="block px-3 py-2 text-gray-600 hover:text-navy"
                onClick={toggleMenu}
              >
                GitHub
              </a>
              <div className="px-3 py-2">
                <Button className="w-full bg-navy hover:bg-navy/90 text-white">
                  Documentation
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
