"""
Transaction categorization service that exactly matches the original working code.
Uses the proven 4-step approach: Keywords → User Labels → Fuzzy Matching → AI
"""

import logging
import re
import uuid
import json
import os
from typing import Any, Dict, List
from difflib import get_close_matches

import pandas as pd

logger = logging.getLogger(__name__)

# Exact categories from the original working code
CATEGORIES = [
    "Membership Fee Income",
    "Mentoring Fee Income",
    "Workshop and Events Expense",
    "Software Expense",
    "Marketing Expense",
    "Bank Fees",
    "Transport Expense",
    "Uncategorized"
]

# Exact keyword mapping from the original working code
KEYWORD_MAP = {
    "income":                   "Membership Fee Income",
    "fee":                      "Membership Fee Income",
    "revenue":                  "Mentoring Fee Income",
    "POS":                      "Workshop and Events Expense",
    "ZOOM":                     "Software Expense",
    "Gaschnitz":                "Marketing Expense",
    "Kontoführung":             "Bank Fees",
    "Bereitstellung Debitkarte":"Bank Fees",
    "Habenzinsen":              "Bank Fees",
    "Kest":                     "Bank Fees",
}

# User labels storage directory
USER_LABELS_DIR = "storage/user_labels"


def load_user_labels(source_id: str) -> Dict[str, str]:
    """Load user labels from JSON file for a specific source."""
    os.makedirs(USER_LABELS_DIR, exist_ok=True)
    user_labels_file = os.path.join(USER_LABELS_DIR, f"user_labels_{source_id}.json")

    if os.path.exists(user_labels_file):
        try:
            with open(user_labels_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"Error loading user labels: {e}")
    return {}


def save_user_labels(source_id: str, user_labels: Dict[str, str]):
    """Save user labels to JSON file for a specific source."""
    os.makedirs(USER_LABELS_DIR, exist_ok=True)
    user_labels_file = os.path.join(USER_LABELS_DIR, f"user_labels_{source_id}.json")

    try:
        with open(user_labels_file, "w", encoding="utf-8") as f:
            json.dump(user_labels, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.warning(f"Error saving user labels: {e}")


def categorize(text: str, amount: float, user_labels: Dict[str, str]) -> str:
    """
    Exact categorization logic from the original working code.
    Return the first matching category for *text* or 'Uncategorized'.
    """
    # Step 1: Keyword matching (3-letter words like "fee" match *everything*; keep the regex list short!)
    for pattern, cat in KEYWORD_MAP.items():
        if re.search(pattern, text, flags=re.I):
            logger.debug(f"Keyword match: '{pattern}' -> {cat}")
            return cat

    # Step 2: Exact user label match
    if text in user_labels:
        logger.debug(f"User label match: '{text}' -> {user_labels[text]}")
        return user_labels[text]

    # Step 3: Fuzzy matching with user labels
    similar = get_close_matches(text, user_labels.keys(), n=1, cutoff=0.85)
    if similar:
        suggestion = user_labels[similar[0]]
        logger.info(f"Similar match found: '{text}' -> {suggestion}")
        return suggestion

    # Step 4: AI fallback (will be implemented when LLM is available)
    try:
        from app.utils.llm_factory import get_llm_client

        llm = get_llm_client()
        prompt = (
            "You are a finance assistant. Classify the transaction below.\n"
            f"Always choose exactly one of the following categories:\n"
            f"{', '.join(CATEGORIES)}.\n\n"
            "Rules:\n"
            "- Negative amounts are expenses.\n"
            "- Positive amounts are income.\n"
            "- Names like 'Sara Mari' often mean reimbursements or personal transactions such as expenses for events.\n"
            "- Names with a positive amount often mean fees.\n"
            "- Real estate, restaurants, hotels, bars, cafes or venues → 'Workshop and Events Expense'\n"
            "- Generic or unclear → 'Uncategorized'\n\n"
            f"Description: \"{text}\"\nAmount: {amount:.2f}\nCategory:"
        )

        gpt_answer = llm.generate(prompt).strip()

        # Validate that the AI response is a valid category
        if gpt_answer in CATEGORIES:
            logger.info(f"AI categorization: '{text[:40]}...' ({amount:.2f}) -> {gpt_answer}")
            return gpt_answer
        else:
            logger.warning(f"AI returned invalid category '{gpt_answer}', using Uncategorized")

    except Exception as e:
        logger.debug(f"AI categorization failed: {e}")

    # Fallback to Uncategorized
    return "Uncategorized"


def extract_description(row: Dict[str, Any]) -> str:
    """
    Extract description text exactly like the original code:
    descriptor = f"{row['Partner Name']} {row['Booking details']}"
    """
    partner_name = str(row.get('Partner Name', '')).strip()
    booking_details = str(row.get('Booking details', '')).strip()

    # Handle None values
    if partner_name.lower() in ['none', 'nan', '']:
        partner_name = ''
    if booking_details.lower() in ['none', 'nan', '']:
        booking_details = ''

    # Combine exactly like the original code
    descriptor = f"{partner_name} {booking_details}".strip()
    return descriptor


def extract_amount(row: Dict[str, Any]) -> float:
    """
    Extract amount exactly like the original code:
    amount = float(row["Amount"].replace(",", ""))
    """
    try:
        amount_str = str(row.get("Amount", "0"))
        # Handle comma as decimal separator (European format)
        amount_str = amount_str.replace(",", ".")
        return float(amount_str)
    except (ValueError, TypeError):
        return 0.0


def create_clusters(source_id: str, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Create category clusters from categorized transactions, matching original structure."""
    category_stats = {}

    # Group by category (use 'Category' field like original)
    for trans in transactions:
        category = trans.get('Category', trans.get('category', 'Uncategorized'))
        if category not in category_stats:
            category_stats[category] = {
                'count': 0,
                'total': 0.0,
                'transactions': []
            }

        category_stats[category]['count'] += 1
        category_stats[category]['total'] += trans.get('amount', 0.0)
        category_stats[category]['transactions'].append(trans)

    # Create clusters with proper structure for the frontend
    clusters = []
    for category, stats in category_stats.items():
        # Create sample rows with the expected structure
        sample_rows = []
        for trans in stats['transactions'][:5]:  # Show up to 5 samples
            sample_row = {
                'transaction_date': trans.get('Booking Date', ''),
                'description': trans.get('description_text', ''),
                'amount': trans.get('amount', 0.0)
            }
            sample_rows.append(sample_row)

        cluster = {
            'id': str(uuid.uuid4()),  # Generate proper UUID for database
            'source_id': source_id,  # Add source_id for database storage
            'label': category,
            'row_count': stats['count'],
            'amount_total': round(stats['total'], 2),
            'is_income': stats['total'] > 0,
            'confidence': 1.0,  # Simple categorization is always confident
            'rules': [],  # Add empty rules for database storage
            'sample_rows': sample_rows
        }
        clusters.append(cluster)

    return clusters


def categorize_transactions_simple(
    source_id: str,
    data: List[Dict[str, Any]],
    config: Dict[str, Any]  # Keep for compatibility but not used
) -> Dict[str, Any]:
    """
    Transaction categorization that exactly matches the original working code.
    Uses the proven 4-step approach: Keywords → User Labels → Fuzzy Matching → AI
    """
    if not data:
        raise ValueError("No data provided for categorization")

    logger.info(f"Starting categorization for {len(data)} transactions using original algorithm")

    # Load user labels for this source
    user_labels = load_user_labels(source_id)

    categorized_transactions = []
    uncategorized = []  # Track uncategorized for potential user review

    # Process each transaction exactly like the original code
    for i, row in enumerate(data):
        if i % 20 == 0:  # Progress every 20 transactions
            logger.info(f"Processing transaction {i+1}/{len(data)}")

        # Extract data exactly like original: descriptor = f"{row['Partner Name']} {row['Booking details']}"
        descriptor = extract_description(row)
        amount = extract_amount(row)

        # Categorize using the exact original logic
        category = categorize(descriptor, amount, user_labels)

        # Create categorized transaction
        categorized_transaction = row.copy()
        categorized_transaction['Category'] = category  # Use 'Category' like original
        categorized_transaction['category'] = category  # Also add lowercase for compatibility
        categorized_transaction['description_text'] = descriptor
        categorized_transaction['amount'] = amount

        categorized_transactions.append(categorized_transaction)

        # Track uncategorized items like the original
        if category == "Uncategorized":
            uncategorized.append((descriptor, amount, categorized_transaction))

    # Save user labels (even if unchanged, for consistency)
    save_user_labels(source_id, user_labels)

    # Create clusters from categorized transactions
    clusters = create_clusters(source_id, categorized_transactions)

    logger.info(f"Categorization complete: {len(clusters)} categories, {len(uncategorized)} uncategorized")

    return {
        "source_id": source_id,
        "total_rows": len(data),
        "clustered_rows": len(data),
        "unclustered_rows": len(uncategorized),
        "cluster_count": len(clusters),
        "clusters": clusters
    }
