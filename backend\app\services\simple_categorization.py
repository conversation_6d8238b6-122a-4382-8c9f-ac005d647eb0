"""
Ultra-simple transaction categorization service.
No AI, no complex logic, just fast keyword matching.
"""

import logging
import re
from typing import Any, Dict, List

import pandas as pd

logger = logging.getLogger(__name__)

# Default categories
DEFAULT_CATEGORIES = [
    "Income - Fees",
    "Income - Revenue", 
    "Income - Other",
    "Expense - Software",
    "Expense - Marketing",
    "Expense - Events",
    "Expense - Transport",
    "Expense - Bank Fees",
    "Expense - Office",
    "Expense - Other",
    "Uncategorized"
]

# Simple keyword mappings
KEYWORDS = {
    # German banking terms
    "bereitstellung": "Expense - Bank Fees",
    "kontoführung": "Expense - Bank Fees", 
    "kest": "Expense - Bank Fees",
    "habenzinsen": "Income - Other",
    "spesen": "Expense - Bank Fees",
    "gebühr": "Expense - Bank Fees",
    
    # English terms
    "zoom": "Expense - Software",
    "fee": "Income - Fees",
    "taxi": "Expense - Transport",
    "hotel": "Expense - Events",
    "linkedin": "Expense - Marketing",
    "mentoring": "Income - Fees",
    "membership": "Income - Fees",
    "software": "Expense - Software",
    "marketing": "Expense - Marketing",
    "bank": "Expense - Bank Fees",
    
    # Business patterns
    "vienna": "Income - Fees",
    "professional": "Income - Fees",
    "transactions": "Income - Fees",
    "wildling": "Expense - Events",
    "cakesie": "Expense - Events",
    "gaschnitz": "Expense - Marketing",
    "mugs": "Expense - Marketing"
}


def categorize_by_keywords(text: str) -> str:
    """Categorize transaction by keyword matching."""
    if not text:
        return "Uncategorized"
    
    text_lower = text.lower()
    
    # Check each keyword
    for keyword, category in KEYWORDS.items():
        if keyword in text_lower:
            logger.debug(f"Keyword match: '{keyword}' -> {category}")
            return category
    
    return "Uncategorized"


def categorize_by_amount(amount: float) -> str:
    """Simple categorization by amount."""
    if amount > 0:
        return "Income - Other"
    else:
        return "Expense - Other"


def extract_description(row: Dict[str, Any]) -> str:
    """Extract description text from transaction row."""
    # Try common description field names
    desc_fields = ['Booking details', 'description', 'Description', 'details', 'Details', 'memo', 'Memo']
    
    for field in desc_fields:
        if field in row and row[field]:
            value = str(row[field]).strip()
            if value and value.lower() not in ['nan', 'none', '']:
                return value
    
    return ""


def extract_amount(row: Dict[str, Any]) -> float:
    """Extract amount from transaction row."""
    # Try common amount field names
    amount_fields = ['Amount', 'amount', 'value', 'Value', 'sum', 'Sum']
    
    for field in amount_fields:
        if field in row and row[field] is not None:
            try:
                return float(row[field])
            except (ValueError, TypeError):
                continue
    
    return 0.0


def create_clusters(source_id: str, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Create simple category clusters from categorized transactions."""
    category_stats = {}
    
    # Group by category
    for trans in transactions:
        category = trans.get('category', 'Uncategorized')
        if category not in category_stats:
            category_stats[category] = {
                'count': 0,
                'total': 0.0,
                'transactions': []
            }
        
        category_stats[category]['count'] += 1
        category_stats[category]['total'] += trans.get('amount', 0.0)
        category_stats[category]['transactions'].append(trans)
    
    # Create clusters
    clusters = []
    for category, stats in category_stats.items():
        cluster = {
            'id': f"cluster_{category.replace(' ', '_').replace('-', '_').lower()}",
            'source_id': source_id,  # Add source_id for database storage
            'label': category,
            'row_count': stats['count'],
            'amount_total': round(stats['total'], 2),
            'is_income': stats['total'] > 0,
            'confidence': 1.0,  # Simple categorization is always confident
            'rules': [],  # Add empty rules for database storage
            'sample_rows': stats['transactions'][:3]  # First 3 as samples
        }
        clusters.append(cluster)
    
    return clusters


def categorize_transactions_simple(
    source_id: str,
    data: List[Dict[str, Any]],
    config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Ultra-simple transaction categorization.
    Fast, reliable, no AI calls.
    """
    if not data:
        raise ValueError("No data provided for categorization")
    
    logger.info(f"Starting simple categorization for {len(data)} transactions")
    
    categorized_transactions = []
    
    # Process each transaction
    for i, row in enumerate(data):
        if i % 20 == 0:  # Progress every 20 transactions
            logger.info(f"Processing transaction {i+1}/{len(data)}")
        
        # Extract data
        description = extract_description(row)
        amount = extract_amount(row)
        
        # Categorize
        category = categorize_by_keywords(description)
        if category == "Uncategorized":
            category = categorize_by_amount(amount)
        
        # Create categorized transaction
        categorized_transaction = row.copy()
        categorized_transaction['category'] = category
        categorized_transaction['description_text'] = description
        categorized_transaction['amount'] = amount
        
        categorized_transactions.append(categorized_transaction)
    
    # Create clusters
    clusters = create_clusters(source_id, categorized_transactions)
    
    logger.info(f"Categorization complete: {len(clusters)} categories")
    
    return {
        "source_id": source_id,
        "total_rows": len(data),
        "clustered_rows": len(data),
        "unclustered_rows": 0,
        "cluster_count": len(clusters),
        "clusters": clusters
    }
