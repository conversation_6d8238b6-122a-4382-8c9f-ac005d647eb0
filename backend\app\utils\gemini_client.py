import logging
import os
import json
from typing import Any, Dict, List, Optional

import google.generativeai as genai
from app.core.config import settings

logger = logging.getLogger(__name__)

class GeminiClient:
    """Client for interacting with Google's Gemini LLM."""

    def __init__(self):
        # Get API key from environment variable or settings
        api_key = os.environ.get("GEMINI_API_KEY") or getattr(settings, "GEMINI_API_KEY", "")

        if not api_key:
            logger.error("No Gemini API key found. Set GEMINI_API_KEY in environment or settings.")
            raise ValueError("Gemini API key is required")

        # Configure the Gemini API
        genai.configure(api_key=api_key)

        # Set up the model
        self.model = genai.GenerativeModel('gemini-pro')
        logger.info("Gemini LLM client initialized successfully")

    def classify_data(self, text: str, categories: List[str]) -> Dict[str, float]:
        """
        Classify text into predefined categories.

        Args:
            text: Text to classify
            categories: List of possible categories

        Returns:
            Dictionary mapping categories to confidence scores
        """
        try:
            # Create prompt for classification
            prompt = f"""
            Classify the following text into one of these categories: {', '.join(categories)}

            Text: {text}

            Return your answer as a JSON object with categories as keys and confidence scores (0-1) as values.
            Only include categories with non-zero confidence.

            Example: {{"Category1": 0.8, "Category2": 0.2}}
            """

            # Generate response
            response = self.model.generate_content(prompt)

            # Extract and parse JSON from response
            result_text = response.text
            logger.debug(f"Gemini response: {result_text}")

            # Try to extract JSON from the response
            json_match = result_text.strip()
            if json_match.startswith("```json"):
                json_match = json_match.replace("```json", "").replace("```", "").strip()
            elif json_match.startswith("```"):
                json_match = json_match.replace("```", "").strip()

            # Parse the JSON
            result = json.loads(json_match)

            # Ensure all categories have values
            for category in categories:
                if category not in result:
                    result[category] = 0.0

            return result

        except Exception as e:
            logger.error(f"Error classifying data with Gemini: {e}")
            # Fallback to simple classification
            return {categories[0]: 1.0}

    def extract_entities(self, text: str, entity_types: List[str]) -> Dict[str, List[str]]:
        """
        Extract entities from text.

        Args:
            text: Text to extract entities from
            entity_types: Types of entities to extract (e.g., "person", "organization")

        Returns:
            Dictionary mapping entity types to lists of extracted entities
        """
        try:
            # Create prompt for entity extraction
            prompt = f"""
            Extract the following entity types from the text: {', '.join(entity_types)}

            Text: {text}

            Return your answer as a JSON object with entity types as keys and lists of extracted entities as values.

            Example: {{"person": ["John Doe", "Jane Smith"], "organization": ["Acme Corp"]}}
            """

            # Generate response
            response = self.model.generate_content(prompt)

            # Extract and parse JSON from response
            result_text = response.text
            logger.debug(f"Gemini response: {result_text}")

            # Try to extract JSON from the response
            json_match = result_text.strip()
            if json_match.startswith("```json"):
                json_match = json_match.replace("```json", "").replace("```", "").strip()
            elif json_match.startswith("```"):
                json_match = json_match.replace("```", "").strip()

            # Parse the JSON
            result = json.loads(json_match)

            # Ensure all entity types have values
            for entity_type in entity_types:
                if entity_type not in result:
                    result[entity_type] = []

            return result

        except Exception as e:
            logger.error(f"Error extracting entities with Gemini: {e}")
            # Fallback to empty results
            return {entity_type: [] for entity_type in entity_types}

    def generate_sql(self, question: str, table_schema: Dict[str, List[str]]) -> str:
        """
        Generate SQL from a natural language question.

        Args:
            question: Natural language question
            table_schema: Dictionary mapping table names to lists of column names

        Returns:
            Generated SQL query
        """
        try:
            # Format the schema information
            schema_str = ""
            for table, columns in table_schema.items():
                schema_str += f"Table: {table}\nColumns: {', '.join(columns)}\n"

            # Create prompt for SQL generation
            prompt = f"""
            Generate a SQL query for the following question using the provided schema:

            Schema:
            {schema_str}

            Question: {question}

            Return only the SQL query without any explanation.
            """

            # Generate response
            response = self.model.generate_content(prompt)

            # Extract SQL from response
            result = response.text.strip()
            if result.startswith("```sql"):
                result = result.replace("```sql", "").replace("```", "").strip()
            elif result.startswith("```"):
                result = result.replace("```", "").strip()

            return result

        except Exception as e:
            logger.error(f"Error generating SQL with Gemini: {e}")
            # Fallback to simple query
            return f"SELECT * FROM {list(table_schema.keys())[0]} LIMIT 10;"

    def generate(self, prompt: str) -> str:
        """
        Generate text from a prompt.

        Args:
            prompt: Text prompt

        Returns:
            Generated text
        """
        try:
            # Generate response with timeout
            import concurrent.futures

            def _generate():
                return self.model.generate_content(prompt)

            # Use ThreadPoolExecutor with a timeout
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(_generate)
                try:
                    response = future.result(timeout=30)  # 30 second timeout
                    logger.info("Successfully generated response from Gemini")
                    return response.text
                except concurrent.futures.TimeoutError:
                    logger.error("Gemini API request timed out after 30 seconds")
                    return "Error: Request to Gemini API timed out. Please try again later."

        except Exception as e:
            logger.error(f"Error generating text with Gemini: {e}")
            # Return a fallback response
            return "Error generating response from LLM."


# Use lazy loading for the Gemini client
_gemini_client = None

def get_gemini_client():
    """
    Get or create the Gemini client instance.

    Returns:
        GeminiClient: The Gemini client instance
    """
    global _gemini_client
    if _gemini_client is None:
        try:
            _gemini_client = GeminiClient()
        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}")
            raise
    return _gemini_client